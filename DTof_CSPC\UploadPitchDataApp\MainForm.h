#ifndef MAIN_H
#define MAIN_H

#include <QWidget>
#include "Comm.h"
#include "widget.h"

namespace Ui
{
    class Main;
}

class Main : public QWidget
{
    Q_OBJECT

public:
    explicit Main(QWidget *parent = nullptr);
    ~Main();

private slots:
    void on_uartUiBtn_clicked();

    void on_mesUiBtn_clicked();

private:
    Ui::Main *ui;

    Widget* mesUi{nullptr};
    Comm* uartUi{nullptr};
};

#endif // MAIN_H
