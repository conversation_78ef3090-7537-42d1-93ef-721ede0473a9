{"inputs": [{"path": "CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeMinGWFindMake.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeDetermineSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeSystem.cmake.in"}, {"isGenerated": true, "path": "build/CMakeFiles/3.21.2/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeDetermineCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Platform/Windows-Determine-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeDetermineCompilerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeCompilerIdDetection.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/ADSP-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/ARMCC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/ARMClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/AppleClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/Borland-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/Clang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/Cray-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/GHS-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/IAR-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/Intel-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/MSVC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/NVHPC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/PGI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/PathScale-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/ROCMClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/SCO-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/TI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/Watcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/GNU-FindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "build/CMakeFiles/3.21.2/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Platform/Windows-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Platform/Windows-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeDetermineRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeRCCompiler.cmake.in"}, {"isGenerated": true, "path": "build/CMakeFiles/3.21.2/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Platform/Windows-windres.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeTestRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeTestCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeDetermineCompilerABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeParseImplicitIncludeInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeParseLibraryArchitecture.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeCXXCompilerABI.cpp"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeDetermineCompileFeatures.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Internal/FeatureTesting.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "build/CMakeFiles/3.21.2/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/Platform/Windows-GNU-CXX-ABI.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5/Qt5Config.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5/Qt5Config.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5/Qt5ModuleLocation.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeParseArguments.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgIconPlugin.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgPlugin.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QVirtualKeyboardPlugin.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QWebGLIntegrationPlugin.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Widgets/Qt5Widgets_QWindowsVistaStylePlugin.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/CMakeParseArguments.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Sql/Qt5SqlConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Sql/Qt5SqlConfig.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Sql/Qt5Sql_QODBCDriverPlugin.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Sql/Qt5Sql_QPSQLDriverPlugin.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5Sql/Qt5Sql_QSQLiteDriverPlugin.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5SerialPort/Qt5SerialPortConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/cmake/Qt5SerialPort/Qt5SerialPortConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/FindPython3.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/FindPython/Support.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Programs/CMake/share/cmake-3.21/Modules/FindPackageMessage.cmake"}, {"path": "res.qrc"}], "kind": "cmakeFiles", "paths": {"build": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build", "source": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp"}, "version": {"major": 1, "minor": 0}}