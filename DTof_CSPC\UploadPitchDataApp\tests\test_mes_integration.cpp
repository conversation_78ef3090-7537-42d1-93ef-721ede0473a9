#include <QCoreApplication>
#include <QDebug>
#include <QDir>
#include <QFileInfo>
#include <QTimer>
#include <QDateTime>
#include "../widget.h"
#include "../CSV/CSVReader.h"
#include "../CSV/CSVWriter.h"
#include "../Pojo/PitchData.h"
#include "../Pojo/MESData.h"

/**
 * MES功能集成测试程序
 * 直接调用Widget的MES上传功能，检查日志和备份文件
 */

class MESIntegrationTester : public QObject {
    Q_OBJECT

public:
    MESIntegrationTester(QObject *parent = nullptr) : QObject(parent) {
        // 设置测试环境
        setupTestEnvironment();
        
        // 创建Widget实例
        widget = new Widget();
        
        // 连接信号槽用于监控
        connect(widget, &Widget::updateUploadMESSizeCnt, this, &MESIntegrationTester::onUploadCountUpdated);
        connect(widget, &Widget::updateMesUploadSuccessSizeCnt, this, &MESIntegrationTester::onSuccessCountUpdated);
        connect(widget, &Widget::updateMesUploadErrSizeCnt, this, &MESIntegrationTester::onErrorCountUpdated);
    }
    
    ~MESIntegrationTester() {
        delete widget;
    }
    
    void runIntegrationTest() {
        qDebug() << "=== MES功能集成测试开始 ===";
        qDebug() << "测试时间:" << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
        
        // 1. 检查测试环境
        if (!checkTestEnvironment()) {
            qDebug() << "❌ 测试环境检查失败";
            QCoreApplication::quit();
            return;
        }
        
        // 2. 测试CSV读取功能
        testCSVReading();
        
        // 3. 测试工作拷贝创建
        testWorkingCopyCreation();
        
        // 4. 测试数据处理逻辑
        testDataProcessing();
        
        // 5. 模拟MES上传流程（不连接真实数据库）
        testMESUploadFlow();
        
        // 6. 检查生成的文件和日志
        checkGeneratedFiles();
        
        qDebug() << "=== 集成测试完成 ===";
        
        // 延迟退出，确保所有操作完成
        QTimer::singleShot(1000, this, []() {
            QCoreApplication::quit();
        });
    }

private slots:
    void onUploadCountUpdated(int count) {
        qDebug() << "📊 上传数据数量更新:" << count;
    }
    
    void onSuccessCountUpdated(int count) {
        qDebug() << "✅ 成功上传数量:" << count;
    }
    
    void onErrorCountUpdated(int count) {
        qDebug() << "❌ 上传失败数量:" << count;
    }

private:
    Widget* widget;
    QString testDataPath;
    QString testOutputDir;
    CSVReader csvReader;
    CSVWriter csvWriter;
    
    void setupTestEnvironment() {
        // 设置测试数据路径
        testDataPath = QCoreApplication::applicationDirPath() + "/俯仰角视觉检测_195_20250623143833252.csv";
        testOutputDir = QCoreApplication::applicationDirPath() + "/test_output/";
        
        // 创建测试输出目录
        QDir().mkpath(testOutputDir);
        
        qDebug() << "测试数据路径:" << testDataPath;
        qDebug() << "测试输出目录:" << testOutputDir;
    }
    
    bool checkTestEnvironment() {
        qDebug() << "\n--- 检查测试环境 ---";
        
        // 检查测试数据文件
        QFileInfo testFile(testDataPath);
        if (!testFile.exists()) {
            qDebug() << "❌ 测试数据文件不存在:" << testDataPath;
            return false;
        }
        qDebug() << "✅ 测试数据文件存在:" << testFile.size() << "字节";
        
        // 检查配置文件
        QString configPath = QCoreApplication::applicationDirPath() + "/MesInfo.xml";
        QFileInfo configFile(configPath);
        if (!configFile.exists()) {
            qDebug() << "⚠️  配置文件不存在:" << configPath;
            qDebug() << "   将使用默认配置进行测试";
        } else {
            qDebug() << "✅ 配置文件存在:" << configPath;
        }
        
        // 检查日志目录
        QString logDir = QCoreApplication::applicationDirPath() + "/Log/";
        QDir().mkpath(logDir);
        qDebug() << "✅ 日志目录:" << logDir;
        
        return true;
    }
    
    void testCSVReading() {
        qDebug() << "\n--- 测试CSV读取功能 ---";
        
        QVector<PitchData> data = csvReader.readDataFromCsv(testDataPath);
        qDebug() << "📄 CSV读取结果:";
        qDebug() << "   数据条数:" << data.size();
        
        if (!data.isEmpty()) {
            qDebug() << "   第一条数据:";
            qDebug() << "     电机标签:" << data[0].getNbr();
            qDebug() << "     MCU ID:" << data[0].getMcuID();
            qDebug() << "     固件版本:" << data[0].getFirmwareVersion();
            qDebug() << "     录入状态:" << static_cast<int>(data[0].getUploadStatus());
            
            // 统计各种状态
            int notUploaded = 0, uploaded = 0, failed = 0;
            for (const PitchData& item : data) {
                switch (item.getUploadStatus()) {
                    case UploadStatus::NotUploaded: notUploaded++; break;
                    case UploadStatus::Uploaded: uploaded++; break;
                    case UploadStatus::Failed: failed++; break;
                }
            }
            qDebug() << "   状态统计: 未录入:" << notUploaded << " 已录入:" << uploaded << " 失败:" << failed;
        }
    }
    
    void testWorkingCopyCreation() {
        qDebug() << "\n--- 测试工作拷贝创建 ---";
        
        bool isWorkingCopy = csvReader.isWorkingCopy(testDataPath);
        qDebug() << "📋 原始文件是否为工作拷贝:" << (isWorkingCopy ? "是" : "否");
        
        if (!isWorkingCopy) {
            QString workingCopyPath = csvReader.createWorkingCopy(testDataPath);
            if (!workingCopyPath.isEmpty()) {
                qDebug() << "✅ 工作拷贝创建成功:" << workingCopyPath;
                
                // 验证工作拷贝
                bool isNewWorkingCopy = csvReader.isWorkingCopy(workingCopyPath);
                qDebug() << "   新文件是否为工作拷贝:" << (isNewWorkingCopy ? "是" : "否");
                
                // 检查文件大小
                QFileInfo originalFile(testDataPath);
                QFileInfo workingFile(workingCopyPath);
                qDebug() << "   原始文件大小:" << originalFile.size() << "字节";
                qDebug() << "   工作拷贝大小:" << workingFile.size() << "字节";
            } else {
                qDebug() << "❌ 工作拷贝创建失败";
            }
        }
    }
    
    void testDataProcessing() {
        qDebug() << "\n--- 测试数据处理逻辑 ---";
        
        QVector<PitchData> allData = csvReader.readDataWithStatus(testDataPath);
        qDebug() << "📊 数据处理测试:";
        qDebug() << "   原始数据:" << allData.size() << "条";
        
        // 测试去重
        QVector<PitchData> uniqueData = csvReader.removeDuplicatesKeepLatest(allData);
        qDebug() << "   去重后:" << uniqueData.size() << "条";
        
        // 测试过滤
        QVector<PitchData> needsProcessing = csvReader.filterNeedsProcessing(uniqueData);
        qDebug() << "   需要处理:" << needsProcessing.size() << "条";
        
        // 测试获取真正需要上传的数据
        QVector<PitchData> actualUploadData = csvReader.getActualUploadData(allData);
        qDebug() << "   真正需要上传:" << actualUploadData.size() << "条";
        
        // 计算处理效率
        if (allData.size() > 0) {
            double reductionRate = (1.0 - (double)actualUploadData.size() / allData.size()) * 100;
            qDebug() << "   数据减少率:" << QString::number(reductionRate, 'f', 1) << "%";
        }
    }
    
    void testMESUploadFlow() {
        qDebug() << "\n--- 测试MES上传流程 ---";
        
        // 注意：这里不会真正连接数据库，只是测试数据流程
        qDebug() << "⚠️  注意：此测试不会连接真实MES数据库";
        
        try {
            // 读取数据
            QVector<PitchData> pitchData = csvReader.readDataWithStatus(testDataPath);
            if (pitchData.isEmpty()) {
                qDebug() << "❌ 没有数据可供上传";
                return;
            }
            
            // 模拟构建MES数据（简化版本）
            QVector<MESData> mesData;
            for (const PitchData& item : pitchData) {
                if (item.needsProcessing()) {
                    MESData mesItem;
                    mesItem.nbr = item.getNbr();
                    mesItem.mcuID = item.getMcuID();
                    mesItem.testResult = item.getTestResult();
                    mesItem.act1 = item.getPitchAngleValueMin();
                    mesItem.act2 = item.getPitchAngleValueMax();
                    mesData.append(mesItem);
                }
            }
            
            qDebug() << "📤 准备上传MES数据:" << mesData.size() << "条";
            
            // 这里可以调用widget的上传槽函数（如果需要的话）
            // widget->uploadMesDataSlot(mesData, QDateTime::currentDateTime().toString("yyyyMMddHHmmss"));
            
        } catch (const std::exception& e) {
            qDebug() << "❌ MES上传流程测试异常:" << e.what();
        }
    }
    
    void checkGeneratedFiles() {
        qDebug() << "\n--- 检查生成的文件和日志 ---";
        
        QString appDir = QCoreApplication::applicationDirPath();
        
        // 检查日志文件
        QString logDir = appDir + "/Log/";
        QDir logDirectory(logDir);
        if (logDirectory.exists()) {
            QStringList logFiles = logDirectory.entryList(QStringList() << "*.log", QDir::Files);
            qDebug() << "📝 日志文件:" << logFiles.size() << "个";
            for (const QString& logFile : logFiles) {
                QFileInfo fileInfo(logDir + logFile);
                qDebug() << "   " << logFile << " (" << fileInfo.size() << "字节, " 
                         << fileInfo.lastModified().toString("yyyy-MM-dd hh:mm:ss") << ")";
            }
        } else {
            qDebug() << "⚠️  日志目录不存在:" << logDir;
        }
        
        // 检查工作拷贝文件
        QDir appDirectory(appDir);
        QStringList workingCopies = appDirectory.entryList(QStringList() << "*工作副本*.csv", QDir::Files);
        qDebug() << "📋 工作拷贝文件:" << workingCopies.size() << "个";
        for (const QString& file : workingCopies) {
            QFileInfo fileInfo(appDir + "/" + file);
            qDebug() << "   " << file << " (" << fileInfo.size() << "字节)";
        }
        
        // 检查结果文件
        QStringList resultFiles = appDirectory.entryList(QStringList() << "*上传*.csv" << "*异常*.csv", QDir::Files);
        qDebug() << "📊 结果文件:" << resultFiles.size() << "个";
        for (const QString& file : resultFiles) {
            QFileInfo fileInfo(appDir + "/" + file);
            qDebug() << "   " << file << " (" << fileInfo.size() << "字节)";
        }
        
        // 检查配置文件
        QFileInfo configFile(appDir + "/MesInfo.xml");
        if (configFile.exists()) {
            qDebug() << "⚙️  配置文件: MesInfo.xml (" << configFile.size() << "字节)";
        }
    }
};

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    // 设置应用程序信息
    app.setApplicationName("MES Integration Tester");
    app.setApplicationVersion("1.0");
    
    qDebug() << "MES功能集成测试程序启动";
    qDebug() << "应用程序目录:" << QCoreApplication::applicationDirPath();
    
    MESIntegrationTester tester;
    
    // 延迟启动测试，确保应用程序完全初始化
    QTimer::singleShot(100, &tester, &MESIntegrationTester::runIntegrationTest);
    
    return app.exec();
}

#include "test_mes_integration.moc"
