#ifndef WIDGET_H
#define WIDGET_H

#include <QWidget>
#include <QtSql/QSqlDatabase>
#include "Config/ConfigLoader.h"
#include "Config/XmlConfig.h"

#include "CSV/CSVReader.h"
#include "CSV/CSVWriter.h"

#include "DataStore/DataStore.h"

QT_BEGIN_NAMESPACE
namespace Ui
{
    class Widget;
}
QT_END_NAMESPACE

class Widget : public QWidget
{
    Q_OBJECT

public:
    Widget(QWidget *parent = nullptr);
    ~Widget();


signals:
    void uploadMesDataSignal(QVector<MESData>& mesData, const QString& time);

    void updateFirmwareVersionSizeCnt(int size);
    void updateUploadMESSizeCnt(int size);
    void updateMesInfoErrSizeCnt(int size);
    void updateMesUploadErrSizeCnt(int size);
    void updateMesUploadSuccessSizeCnt(int size);

private slots:
    void on_selectUploadFileBtn_clicked();

    void uploadMesDataSlot(QVector<MESData>& mesData, const QString& time);

private:
    Ui::Widget *ui;
    ConfigLoader configLoader;
    XmlConfig config;

    DataStore dataStore;
    QSqlDatabase odbcDB;
    QString mesDataFileName;

    CSVReader csvReader;
    CSVWriter csvWriter;

    bool isHege(float value, float min, float max);

};
#endif // WIDGET_H
