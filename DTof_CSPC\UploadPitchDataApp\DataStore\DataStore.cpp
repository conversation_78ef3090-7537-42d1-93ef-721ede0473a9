#include "DataStore.h"
#include <QDateTime>
#include <QDebug>
#include <QSqlError>
#include <QSqlQuery>

DataStore::DataStore() {
}

uint8_t DataStore::open(QSqlDatabase &db) {
    if (db.open())
        return SqlErrorType::NoError;

    qDebug() << QString::fromUtf8("Database access failed:") << db.lastError().text();
    return SqlErrorType::DataBaseOpenError;
}

uint8_t DataStore::close(QSqlDatabase &db) {
    if (db.isOpen()) {
        qDebug() << QString::fromUtf8("Database connection closed successfully");
        db.close();
    }
    qDebug() << QString::fromUtf8("The database is not open, no need to close");
    return SqlErrorType::NoError;
}

/**
 * @brief 获取虚拟标签
 * 
 * @param db 
 * @param mcuid 
 * @param workOrder 
 * @param topNbr 虚拟标签 
 * @param domain 
 * @return uint8_t 
 */
uint8_t DataStore::getVirtualNbr(QSqlDatabase &db, QString mcuid, QString workOrder, QString &topNbr, QString domain) {
    qDebug() << QString::fromUtf8("Start querying topNbr") << mcuid << " " << workOrder << " " << domain;
    QSqlQuery query(db);
    QString   sql;
    sql = QString("select * from pub.xpid_det where xpid_uid = '%1' and xpid_lot = '%2' and xpid_domain = '%3' with(nolock)")
              .arg(mcuid)
              .arg(workOrder)
              .arg(domain);
    qDebug() << sql;
    try {
        if (!query.exec(sql)) {
            throw QSqlError(query.lastError());
        } else {
            bool hasData = false;
            while (query.next()) {
                hasData = true;
                topNbr  = query.value(2).toString();
            }
            if (false == hasData) {
                qDebug() << QString::fromUtf8("SQL execution succeeded, but no data was found.");
                return SqlErrorType::MesDataNotFindError;
            }
            qDebug() << QString::fromUtf8("SQL execution succeeded:") << topNbr;
            return SqlErrorType::NoError;
        }
    } catch (QSqlError &e) {
        qDebug() << QString::fromUtf8("SQL execution failed: ") << e.text();
        return SqlErrorType::UnknownError;
    }
}

uint8_t DataStore::updataTopNbrForNbr(QSqlDatabase &db, const QString &nbr, const QString &topNbr, const QString &domain) {
    QString time        = "2001-01-01";
    QString currentTime = QDate::currentDate().toString("yyyy-M-d");
    QString sql;
    sql = QString("update pub.xsub0_det set xsub0_relnbr = '%1',xsub0_date ='%2',xsub0__dte01 ='%3' where xsub0_domain = '%4' and xsub0_nbr = '%5'")
              .arg(topNbr)
              .arg(currentTime)
              .arg(time)
              .arg(domain)
              .arg(nbr);
    qDebug() << sql;
    QSqlQuery query(db);
    try {
        if (!query.exec(sql)) {
            throw QSqlError(query.lastError());
        } else {
            return SqlErrorType::NoError;
        }
    } catch (QSqlError &e) {
        qDebug() << QString::fromUtf8("Database operation exception:\"") << sql << "\"";
        qDebug() << QString::fromUtf8("Exception handling: ") << e.text();
        return SqlErrorType::UnknownError;
    }
}

uint8_t DataStore::findXsubTrnbrMax(QSqlDatabase &db, QString xsub_n, uint32_t &trnbr) {
    QSqlQuery query(db);
    QString   sql_str_1, sql_str_2;
    sql_str_1 = QString("select MAX(%1_trnbr) from pub.%1_det where %1_date = '%2'and %1_domain = '001' with(nolock)")
                    .arg(xsub_n)
                    .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd"));  //
    sql_str_2 = QString("select MAX(%1_trnbr) from pub.%2_det with(nolock)").arg(xsub_n).arg(xsub_n);
    qDebug() << "sql_str_1" << sql_str_1;
    try {
        if (!query.exec(sql_str_1)) {
            throw QSqlError(query.lastError());
        } else {
            while (query.next()) {
                // 处理查询结果
                qDebug() << QString::fromUtf8("trnbr:") << trnbr << query.value(0).toString();
                if (query.value(0).toInt() == 0) {
                    break;
                } else {
                    trnbr = query.value(0).toInt() + 1;
                    return SqlErrorType::NoError;
                }
            }
        }
    } catch (QSqlError &e) {
        qDebug() << QString::fromUtf8("Database operation exception: \"") << sql_str_1 << "\"";
        qDebug() << QString::fromUtf8("Exception handling: ") << e.text();
        return SqlErrorType::UnknownError;
    }
    qDebug() << "sql_str_2" << sql_str_2;
    try {
        if (!query.exec(sql_str_2)) {
            throw QSqlError(query.lastError());
        } else {
            while (query.next()) {
                // 处理查询结果
                trnbr = query.value(0).toInt() + 1;
                qDebug() << QString::fromUtf8("trnbr:") << trnbr;
                return SqlErrorType::NoError;
            }
        }
    } catch (QSqlError &e) {
        qDebug() << QString::fromUtf8("Database operation exception:  \"") << sql_str_2 << "\"";
        qDebug() << QString::fromUtf8("Exception handling: ") << e.text();
        return SqlErrorType::UnknownError;
    }
    return SqlErrorType::MesDataNotFindError;
}

uint8_t DataStore::checkMesInfoForNbr(QSqlDatabase &db,
                                      QString &     topNbr,
                                      QString       nbr,
                                      QString       domain,
                                      QString       workOrder,
                                      QString &     SNCode,
                                      QString       snHardwareVersion,
                                      QString       snFirewareVersion) {
    QSqlQuery query(db);
    QString   sql;
    sql = QString("select * from pub.xsub0_det where xsub0_domain = '%1' and xsub0_nbr = '%2' with(nolock)").arg(domain).arg(nbr);
    qDebug() << sql;
    QString workOrderTmp;
    try {
        if (!query.exec(sql)) {
            throw QSqlError(query.lastError());
        } else {
            bool hasData = false;
            while (query.next()) {
                hasData      = true;
                SNCode       = query.value(2).toString();
                workOrderTmp = query.value(3).toString();
                topNbr       = query.value(17).toString();
            }
            if (false == hasData) {
                qDebug() << QString::fromUtf8("No data found");
                return SqlErrorType::MesDataNotFindError;
            }
        }
    } catch (QSqlError &e) {
        qDebug() << QString::fromUtf8("Exception handling: ") << e.text();
        return SqlErrorType::UnknownError;
    }

    qDebug() << "snCodeTmp:" << SNCode << " workOrderTmp:" << workOrderTmp;
    if (workOrderTmp != workOrder) {
        qDebug() << QString::fromUtf8("workorder error");
        return SqlErrorType::WorkOrderError;
    }

    if (21 != SNCode.size() || snHardwareVersion != SNCode.mid(19, 1) || snFirewareVersion != SNCode.mid(20, 1)) {
        qDebug() << QString::fromUtf8("SNCode error");
        return SqlErrorType::SNCodeError;
    }

    return SqlErrorType::NoError;
}

uint8_t DataStore::checkMesInfoForTopNbr(QSqlDatabase &db, QString topNbr, QString preop, QString wordOrder) {
    QSqlQuery query(db);
    QString   sql_str;
    int       op_flag = 0, work_order_flag = 0;
    sql_str = QString("select * from pub.xlbop_mstr where xlbop_nbr = '%1' with(nolock)").arg(topNbr);
    qDebug() << sql_str;
    try {
        if (!query.exec(sql_str)) {
            throw QSqlError(query.lastError());
        } else {
            while (query.next()) {
                work_order_flag = 0;
                op_flag         = 0;
                // 处理查询结果
                if (query.value(1).toString() == preop) {
                    op_flag = 1;  //工序存在
                }
                if (query.value(2).toString() == wordOrder) {
                    work_order_flag = 1;  //正常工单
                }
                if (1 == op_flag) {
                    if (query.value(3).toString() == "") {
                        op_flag = 3;  //关联工序正常
                    } else {
                        op_flag = 2;  //关联工序异常
                    }
                    break;
                }
            }
        }
    } catch (QSqlError &e) {
        qDebug() << QString::fromUtf8("Database operation exception: \"") << sql_str << "\"";
        qDebug() << QString::fromUtf8("Exception handling: ") << e.text();
        return SqlErrorType::UnknownError;
    }
    if (work_order_flag == 1 && op_flag == 3) {  //工单正常 关联工序正常
        return SqlErrorType::NoError;
    } else if (work_order_flag == 1 && op_flag == 2) {  //关联工序不良
        qDebug() << QString::fromUtf8("preop ng ") << preop;
        return SqlErrorType::PreopNGError;
    } else if (work_order_flag == 1 && op_flag == 0) {  //关联工序未找到
        qDebug() << QString::fromUtf8("preop not found") << preop;
        return SqlErrorType::PreopNotFindError;
    } else if (work_order_flag == 0) {  //工单异常
        qDebug() << QString::fromUtf8("query workorder:") << query.value(2).toString() << QString::fromUtf8("config workorder:") << wordOrder;
        return SqlErrorType::WorkOrderError;
    }
}

uint8_t DataStore::uploadMesData(QSqlDatabase &db, const MESData &data) {
    QString sql = createInsertSql(data);
    qDebug() << sql;
    QSqlQuery query(db);
    try {
        if (!query.exec(sql)) {
            throw QSqlError(query.lastError());
        } else {
            return SqlErrorType::NoError;
        }
    } catch (QSqlError &e) {
        qDebug() << QString::fromUtf8("Database operation exception \"") << sql << "\"";
        qDebug() << QString::fromUtf8("Exception handling: ") << e.text();
        return SqlErrorType::UnknownError;
    }
}

QString DataStore::createInsertSql(const MESData &data) {
    QString sql;
    sql =
        QString("insert into pub.xsub6_det (xsub6_domain, xsub6_trnbr, xsub6_nbr, xsub6_op, xsub6_userid, xsub6_date, xsub6_time, xsub6_rslt, xsub6_rsn_code,\
                  xsub6__chr03,xsub6__chr04,xsub6_newid,xsub6_uid,\
                  xsub6_project1,xsub6_stand1,xsub6_act1, xsub6_hege1,\
                  xsub6_project2,xsub6_stand2,xsub6_act2, xsub6_hege2)\
                  values('%1', %2, '%3','%4', 'U%5', '%6', '%7', '%8', '%9',\
                  '%10','%11','%12','%13',\
                  '%14','%15','%16','%17',\
                  '%18','%19','%20','%21')")
            .arg(data.domain)
            .arg(data.trnbr)
            .arg(data.nbr)
            .arg(data.op)
            .arg(data.userID)
            .arg(data.date)
            .arg(data.time)
            .arg(data.testResult)
            .arg(data.rsnCode)
            .arg(data.station)
            .arg(data.topNbr)
            .arg(data.workOrder)
            .arg(data.mcuID)
            .arg(data.projrct1)
            .arg(data.stand1)
            .arg(data.act1)
            .arg(data.hege1)
            .arg(data.projrct2)
            .arg(data.stand2)
            .arg(data.act2)
            .arg(data.hege2);

    return sql;
}
