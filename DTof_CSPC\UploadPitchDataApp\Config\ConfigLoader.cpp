#include "ConfigLoader.h"
#include <QApplication>
#include <QDebug>
#include <QFile>
#include <QString>
#include <QXmlStreamWriter>



ConfigLoader::ConfigLoader() {
}

bool ConfigLoader::createXmlConfig(const XmlConfig &config, const QString &fileName) {
    QFile file(fileName);

    if (file.open(QIODevice::ReadWrite | QIODevice::Truncate)) {
        QXmlStreamWriter writer(&file);
        writer.setAutoFormatting(true);
        writer.writeStartDocument();
        writer.writeStartElement("MesInfo");  // 根节点名称

        writer.writeComment("生产工单");
        writer.writeStartElement("workOrder");
        writer.writeCharacters(config.getWorkOrder());
        writer.writeEndElement();

        writer.writeComment("员工账号");
        writer.writeStartElement("userID");
        writer.writeCharacters(config.getUserID());
        writer.writeEndElement();

        writer.writeComment("上道工序");
        writer.writeStartElement("preop");
        writer.writeCharacters(config.getPreop());
        writer.writeEndElement();

        writer.writeComment("关联工序");
        writer.writeStartElement("op");
        writer.writeCharacters(config.getOp());
        writer.writeEndElement();

        writer.writeComment("当前工站");
        writer.writeStartElement("station");
        writer.writeCharacters(config.getStation());
        writer.writeEndElement();

        writer.writeComment("固件版本");
        writer.writeStartElement("firmwareVersion");
        writer.writeCharacters(config.getFirmwareVersion());
        writer.writeEndElement();

        writer.writeComment("标签硬件版本");
        writer.writeStartElement("snHardwareVersion");
        writer.writeCharacters(config.getSnHardwareVersion());
        writer.writeEndElement();

        writer.writeComment("标签软件版本");
        writer.writeStartElement("snFirmwareVersion");
        writer.writeCharacters(config.getSnFirmwareVersion());
        writer.writeEndElement();

        writer.writeComment("数据库DataSourceName");
        writer.writeStartElement("dataSourceName");
        writer.writeCharacters(config.getDataSourceName());
        writer.writeEndElement();

        writer.writeComment("数据库HostName");
        writer.writeStartElement("hostName");
        writer.writeCharacters(config.getHostName());
        writer.writeEndElement();

        writer.writeComment("数据库Port");
        writer.writeStartElement("port");
        writer.writeCharacters(config.getPort());
        writer.writeEndElement();

        writer.writeComment("数据库dataBaseUserID");
        writer.writeStartElement("dataBaseUserID");
        writer.writeCharacters(config.getDataBaseUserID());
        writer.writeEndElement();

        writer.writeComment("数据库Password");
        writer.writeStartElement("password");
        writer.writeCharacters(config.getPassword());
        writer.writeEndElement();

        // 结束根节点
        writer.writeEndDocument();
        file.flush();  // 刷新写入缓冲区
        file.close();

    } else {
        // 文件创建失败，处理错误
        qDebug() << "Failed to create file:" << file.errorString();
        return false;
    }

    return true;
}

bool ConfigLoader::readXmlconfig(XmlConfig &config, QFile &file) {
    if (file.open(QIODevice::ReadOnly)) {
        QXmlStreamReader reader(&file);
        while (!reader.atEnd() && !reader.hasError()) {
            QXmlStreamReader::TokenType token = reader.readNext();
            if (QXmlStreamReader::StartElement == token) {
                if ("workOrder" == reader.name()) {
                    config.setWorkOrder(reader.readElementText());
                } else if ("userID" == reader.name()) {
                    config.setUserID(reader.readElementText());
                } else if ("preop" == reader.name()) {
                    config.setPreop(reader.readElementText());
                } else if ("op" == reader.name()) {
                    config.setOp(reader.readElementText());
                } else if ("station" == reader.name()) {
                    config.setStation(reader.readElementText());
                } else if ("firmwareVersion" == reader.name()) {
                    config.setFirmwareVersion(reader.readElementText());
                } else if ("snHardwareVersion" == reader.name()) {
                    config.setSnHardwareVersion(reader.readElementText());
                } else if ("snFirmwareVersion" == reader.name()) {
                    config.setSnFirmwareVersion(reader.readElementText());
                } else if ("dataSourceName" == reader.name()) {
                    config.setDataSourceName(reader.readElementText());
                } else if ("hostName" == reader.name()) {
                    config.setHostName(reader.readElementText());
                } else if ("port" == reader.name()) {
                    config.setPort(reader.readElementText());
                } else if ("dataBaseUserID" == reader.name()) {
                    config.setDataBaseUserID(reader.readElementText());
                } else if ("password" == reader.name()) {
                    config.setPassword(reader.readElementText());
                }
            }
        }
    } else {
        return false;
    }
    return true;
}
