[{"directory": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build", "command": "D:\\Programs\\Qt\\Qt5.14.2\\Tools\\mingw730_64\\bin\\x86_64-w64-mingw32-g++.exe -DMAIN_APP_WINDOW_TITLE=\\\"CSPC_UploadPitchDataApp_V0.0.0\\\" -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MESSAGELOGCONTEXT -DQT_NO_DEBUG -DQT_SERIALPORT_LIB -DQT_SQL_LIB -DQT_WIDGETS_LIB @CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp  -finput-charset=UTF-8 -Os -DNDEBUG -std=gnu++11 -o CMakeFiles\\CSPC_UploadPitchDataApp.dir\\CSPC_UploadPitchDataApp_autogen\\mocs_compilation.cpp.obj -c F:\\13_Yapha-Laser-DTof2dMS\\development\\tool\\yapha-proj-mes-02\\DTof_CSPC\\UploadPitchDataApp\\build\\CSPC_UploadPitchDataApp_autogen\\mocs_compilation.cpp", "file": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build/CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp"}, {"directory": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build", "command": "D:\\Programs\\Qt\\Qt5.14.2\\Tools\\mingw730_64\\bin\\x86_64-w64-mingw32-g++.exe -DMAIN_APP_WINDOW_TITLE=\\\"CSPC_UploadPitchDataApp_V0.0.0\\\" -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MESSAGELOGCONTEXT -DQT_NO_DEBUG -DQT_SERIALPORT_LIB -DQT_SQL_LIB -DQT_WIDGETS_LIB @CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp  -finput-charset=UTF-8 -Os -DNDEBUG -std=gnu++11 -o CMakeFiles\\CSPC_UploadPitchDataApp.dir\\main.cpp.obj -c F:\\13_Yapha-Laser-DTof2dMS\\development\\tool\\yapha-proj-mes-02\\DTof_CSPC\\UploadPitchDataApp\\main.cpp", "file": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/main.cpp"}, {"directory": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build", "command": "D:\\Programs\\Qt\\Qt5.14.2\\Tools\\mingw730_64\\bin\\x86_64-w64-mingw32-g++.exe -DMAIN_APP_WINDOW_TITLE=\\\"CSPC_UploadPitchDataApp_V0.0.0\\\" -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MESSAGELOGCONTEXT -DQT_NO_DEBUG -DQT_SERIALPORT_LIB -DQT_SQL_LIB -DQT_WIDGETS_LIB @CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp  -finput-charset=UTF-8 -Os -DNDEBUG -std=gnu++11 -o CMakeFiles\\CSPC_UploadPitchDataApp.dir\\widget.cpp.obj -c F:\\13_Yapha-Laser-DTof2dMS\\development\\tool\\yapha-proj-mes-02\\DTof_CSPC\\UploadPitchDataApp\\widget.cpp", "file": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/widget.cpp"}, {"directory": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build", "command": "D:\\Programs\\Qt\\Qt5.14.2\\Tools\\mingw730_64\\bin\\x86_64-w64-mingw32-g++.exe -DMAIN_APP_WINDOW_TITLE=\\\"CSPC_UploadPitchDataApp_V0.0.0\\\" -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MESSAGELOGCONTEXT -DQT_NO_DEBUG -DQT_SERIALPORT_LIB -DQT_SQL_LIB -DQT_WIDGETS_LIB @CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp  -finput-charset=UTF-8 -Os -DNDEBUG -std=gnu++11 -o CMakeFiles\\CSPC_UploadPitchDataApp.dir\\DataStore\\DataStore.cpp.obj -c F:\\13_Yapha-Laser-DTof2dMS\\development\\tool\\yapha-proj-mes-02\\DTof_CSPC\\UploadPitchDataApp\\DataStore\\DataStore.cpp", "file": "F:/13_Ya<PERSON>-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/DataStore/DataStore.cpp"}, {"directory": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build", "command": "D:\\Programs\\Qt\\Qt5.14.2\\Tools\\mingw730_64\\bin\\x86_64-w64-mingw32-g++.exe -DMAIN_APP_WINDOW_TITLE=\\\"CSPC_UploadPitchDataApp_V0.0.0\\\" -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MESSAGELOGCONTEXT -DQT_NO_DEBUG -DQT_SERIALPORT_LIB -DQT_SQL_LIB -DQT_WIDGETS_LIB @CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp  -finput-charset=UTF-8 -Os -DNDEBUG -std=gnu++11 -o CMakeFiles\\CSPC_UploadPitchDataApp.dir\\Config\\ConfigLoader.cpp.obj -c F:\\13_Yapha-Laser-DTof2dMS\\development\\tool\\yapha-proj-mes-02\\DTof_CSPC\\UploadPitchDataApp\\Config\\ConfigLoader.cpp", "file": "F:/13_Ya<PERSON>-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/Config/ConfigLoader.cpp"}, {"directory": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build", "command": "D:\\Programs\\Qt\\Qt5.14.2\\Tools\\mingw730_64\\bin\\x86_64-w64-mingw32-g++.exe -DMAIN_APP_WINDOW_TITLE=\\\"CSPC_UploadPitchDataApp_V0.0.0\\\" -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MESSAGELOGCONTEXT -DQT_NO_DEBUG -DQT_SERIALPORT_LIB -DQT_SQL_LIB -DQT_WIDGETS_LIB @CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp  -finput-charset=UTF-8 -Os -DNDEBUG -std=gnu++11 -o CMakeFiles\\CSPC_UploadPitchDataApp.dir\\Config\\XmlConfig.cpp.obj -c F:\\13_Yapha-Laser-DTof2dMS\\development\\tool\\yapha-proj-mes-02\\DTof_CSPC\\UploadPitchDataApp\\Config\\XmlConfig.cpp", "file": "F:/13_Ya<PERSON>-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/Config/XmlConfig.cpp"}, {"directory": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build", "command": "D:\\Programs\\Qt\\Qt5.14.2\\Tools\\mingw730_64\\bin\\x86_64-w64-mingw32-g++.exe -DMAIN_APP_WINDOW_TITLE=\\\"CSPC_UploadPitchDataApp_V0.0.0\\\" -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MESSAGELOGCONTEXT -DQT_NO_DEBUG -DQT_SERIALPORT_LIB -DQT_SQL_LIB -DQT_WIDGETS_LIB @CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp  -finput-charset=UTF-8 -Os -DNDEBUG -std=gnu++11 -o CMakeFiles\\CSPC_UploadPitchDataApp.dir\\Pojo\\PitchData.cpp.obj -c F:\\13_Yapha-Laser-DTof2dMS\\development\\tool\\yapha-proj-mes-02\\DTof_CSPC\\UploadPitchDataApp\\Pojo\\PitchData.cpp", "file": "F:/13_Ya<PERSON>-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/Pojo/PitchData.cpp"}, {"directory": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build", "command": "D:\\Programs\\Qt\\Qt5.14.2\\Tools\\mingw730_64\\bin\\x86_64-w64-mingw32-g++.exe -DMAIN_APP_WINDOW_TITLE=\\\"CSPC_UploadPitchDataApp_V0.0.0\\\" -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MESSAGELOGCONTEXT -DQT_NO_DEBUG -DQT_SERIALPORT_LIB -DQT_SQL_LIB -DQT_WIDGETS_LIB @CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp  -finput-charset=UTF-8 -Os -DNDEBUG -std=gnu++11 -o CMakeFiles\\CSPC_UploadPitchDataApp.dir\\Pojo\\MESData.cpp.obj -c F:\\13_Yapha-Laser-DTof2dMS\\development\\tool\\yapha-proj-mes-02\\DTof_CSPC\\UploadPitchDataApp\\Pojo\\MESData.cpp", "file": "F:/13_Ya<PERSON>-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/Pojo/MESData.cpp"}, {"directory": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build", "command": "D:\\Programs\\Qt\\Qt5.14.2\\Tools\\mingw730_64\\bin\\x86_64-w64-mingw32-g++.exe -DMAIN_APP_WINDOW_TITLE=\\\"CSPC_UploadPitchDataApp_V0.0.0\\\" -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MESSAGELOGCONTEXT -DQT_NO_DEBUG -DQT_SERIALPORT_LIB -DQT_SQL_LIB -DQT_WIDGETS_LIB @CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp  -finput-charset=UTF-8 -Os -DNDEBUG -std=gnu++11 -o CMakeFiles\\CSPC_UploadPitchDataApp.dir\\CSV\\CSVReader.cpp.obj -c F:\\13_Yapha-Laser-DTof2dMS\\development\\tool\\yapha-proj-mes-02\\DTof_CSPC\\UploadPitchDataApp\\CSV\\CSVReader.cpp", "file": "F:/13_Ya<PERSON>-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/CSV/CSVReader.cpp"}, {"directory": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build", "command": "D:\\Programs\\Qt\\Qt5.14.2\\Tools\\mingw730_64\\bin\\x86_64-w64-mingw32-g++.exe -DMAIN_APP_WINDOW_TITLE=\\\"CSPC_UploadPitchDataApp_V0.0.0\\\" -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MESSAGELOGCONTEXT -DQT_NO_DEBUG -DQT_SERIALPORT_LIB -DQT_SQL_LIB -DQT_WIDGETS_LIB @CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp  -finput-charset=UTF-8 -Os -DNDEBUG -std=gnu++11 -o CMakeFiles\\CSPC_UploadPitchDataApp.dir\\CSV\\CSVWriter.cpp.obj -c F:\\13_Yapha-Laser-DTof2dMS\\development\\tool\\yapha-proj-mes-02\\DTof_CSPC\\UploadPitchDataApp\\CSV\\CSVWriter.cpp", "file": "F:/13_Ya<PERSON>-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/CSV/CSVWriter.cpp"}, {"directory": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build", "command": "D:\\Programs\\Qt\\Qt5.14.2\\Tools\\mingw730_64\\bin\\x86_64-w64-mingw32-g++.exe -DMAIN_APP_WINDOW_TITLE=\\\"CSPC_UploadPitchDataApp_V0.0.0\\\" -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MESSAGELOGCONTEXT -DQT_NO_DEBUG -DQT_SERIALPORT_LIB -DQT_SQL_LIB -DQT_WIDGETS_LIB @CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp  -finput-charset=UTF-8 -Os -DNDEBUG -std=gnu++11 -o CMakeFiles\\CSPC_UploadPitchDataApp.dir\\Comm.cpp.obj -c F:\\13_Yapha-Laser-DTof2dMS\\development\\tool\\yapha-proj-mes-02\\DTof_CSPC\\UploadPitchDataApp\\Comm.cpp", "file": "F:/13_Ya<PERSON>-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/Comm.cpp"}, {"directory": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build", "command": "D:\\Programs\\Qt\\Qt5.14.2\\Tools\\mingw730_64\\bin\\x86_64-w64-mingw32-g++.exe -DMAIN_APP_WINDOW_TITLE=\\\"CSPC_UploadPitchDataApp_V0.0.0\\\" -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MESSAGELOGCONTEXT -DQT_NO_DEBUG -DQT_SERIALPORT_LIB -DQT_SQL_LIB -DQT_WIDGETS_LIB @CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp  -finput-charset=UTF-8 -Os -DNDEBUG -std=gnu++11 -o CMakeFiles\\CSPC_UploadPitchDataApp.dir\\MainForm.cpp.obj -c F:\\13_Yapha-Laser-DTof2dMS\\development\\tool\\yapha-proj-mes-02\\DTof_CSPC\\UploadPitchDataApp\\MainForm.cpp", "file": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/MainForm.cpp"}, {"directory": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build", "command": "D:\\Programs\\Qt\\Qt5.14.2\\Tools\\mingw730_64\\bin\\x86_64-w64-mingw32-g++.exe -DMAIN_APP_WINDOW_TITLE=\\\"CSPC_UploadPitchDataApp_V0.0.0\\\" -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MESSAGELOGCONTEXT -DQT_NO_DEBUG -DQT_SERIALPORT_LIB -DQT_SQL_LIB -DQT_WIDGETS_LIB @CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp  -finput-charset=UTF-8 -Os -DNDEBUG -std=gnu++11 -o CMakeFiles\\CSPC_UploadPitchDataApp.dir\\CSPC_UploadPitchDataApp_autogen\\EWIEGA46WW\\qrc_res.cpp.obj -c F:\\13_Yapha-Laser-DTof2dMS\\development\\tool\\yapha-proj-mes-02\\DTof_CSPC\\UploadPitchDataApp\\build\\CSPC_UploadPitchDataApp_autogen\\EWIEGA46WW\\qrc_res.cpp", "file": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build/CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp"}]