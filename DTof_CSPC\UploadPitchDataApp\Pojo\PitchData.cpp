#include "PitchData.h"
#include <QDebug>

PitchData::PitchData(const QString &nbr,
                     const QString &mcuID,
                     const QString &firmwareVersion,
                     int            testResult,
                     float          pitchAngleValueMin,
                     float          pitchAngleValueMax,
                     float          pitchAngleValueStandardMin,
                     float          pitchAngleValueStandardMax,
                     UploadStatus   uploadStatus,
                     int            rowIndex)
    : nbr(nbr),
      mcuID(mcuID),
      firmwareVersion(firmwareVersion),
      testResult(testResult),
      pitchAngleValueMin(pitchAngleValueMin),
      pitchAngleValueMax(pitchAngleValueMax),
      pitchAngleValueStandardMin(pitchAngleValueStandardMin),
      pitchAngleValueStandardMax(pitchAngleValueStandardMax),
      uploadStatus(uploadStatus),
      rowIndex(rowIndex) {
    qDebug() << "创建PitchData对象 - 电机标签:" << nbr << "录入状态:" << static_cast<int>(uploadStatus);
}

const QString &PitchData::getNbr() const {
    return this->nbr;
}

const QString &PitchData::getMcuID() const {
    return this->mcuID;
}

const QString &PitchData::getFirmwareVersion() const {
    return this->firmwareVersion;
}

int PitchData::getTestResult() const {
    return this->testResult;
}

float PitchData::getPitchAngleValueMin() const {
    return this->pitchAngleValueMin;
}

float PitchData::getPitchAngleValueMax() const {
    return this->pitchAngleValueMax;
}

float PitchData::getPitchAngleValueStandardMin() const {
    return this->pitchAngleValueStandardMin;
}

float PitchData::getPitchAngleValueStandardMax() const {
    return this->pitchAngleValueStandardMax;
}

// 新增的getter/setter方法实现
UploadStatus PitchData::getUploadStatus() const {
    return this->uploadStatus;
}

void PitchData::setUploadStatus(UploadStatus status) {
    this->uploadStatus = status;
    qDebug() << "更新录入状态 - 电机标签:" << nbr << "新状态:" << static_cast<int>(status);
}

int PitchData::getRowIndex() const {
    return this->rowIndex;
}

void PitchData::setRowIndex(int index) {
    this->rowIndex = index;
}

// 工具方法实现
bool PitchData::isUploaded() const {
    return uploadStatus == UploadStatus::Uploaded;
}

bool PitchData::isFailed() const {
    return uploadStatus == UploadStatus::Failed;
}

bool PitchData::needsProcessing() const {
    return uploadStatus == UploadStatus::NotUploaded || uploadStatus == UploadStatus::Failed;
}
