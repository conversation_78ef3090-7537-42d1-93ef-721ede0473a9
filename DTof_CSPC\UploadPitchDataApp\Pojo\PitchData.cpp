#include "PitchData.h"
#include <QDebug>

PitchData::PitchData(const QString &nbr, const QString &mcuID, const QString &firmwareVersion, int testResult, float pitchAngleValueMin, float pitchAngleValueMax, float pitchAngleValueStandardMin, float pitchAngleValueStandardMax)
    :nbr(nbr),mcuID(mcuID),firmwareVersion(firmwareVersion),testResult(testResult),
     pitchAngleValueMin(pitchAngleValueMin),pitchAngleValueMax(pitchAngleValueMax),pitchAngleValueStandardMin(pitchAngleValueStandardMin),pitchAngleValueStandardMax(pitchAngleValueStandardMax)
{

}

const QString &PitchData::getNbr() const
{
    return this->nbr;
}

const QString &PitchData::getMcuID() const
{
    return this->mcuID;
}

const QString &PitchData::getFirmwareVersion() const
{
    return this->firmwareVersion;
}

int PitchData::getTestResult() const
{
    return this->testResult;
}

float PitchData::getPitchAngleValueMin() const
{
    return this->pitchAngleValueMin;
}

float PitchData::getPitchAngleValueMax() const
{
    return this->pitchAngleValueMax;
}

float PitchData::getPitchAngleValueStandardMin() const
{
    return this->pitchAngleValueStandardMin;
}

float PitchData::getPitchAngleValueStandardMax() const
{
    return this->pitchAngleValueStandardMax;
}
