# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.21

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\Programs\CMake\bin\cmake.exe

# The command to remove a file.
RM = D:\Programs\CMake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	D:\Programs\CMake\bin\cmake-gui.exe -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	D:\Programs\CMake\bin\cmake.exe --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\\CMakeFiles\progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named CSPC_UploadPitchDataApp

# Build rule for target.
CSPC_UploadPitchDataApp: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CSPC_UploadPitchDataApp
.PHONY : CSPC_UploadPitchDataApp

# fast build rule for target.
CSPC_UploadPitchDataApp/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/build
.PHONY : CSPC_UploadPitchDataApp/fast

#=============================================================================
# Target rules for targets named CSPC_UploadPitchDataApp_autogen

# Build rule for target.
CSPC_UploadPitchDataApp_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CSPC_UploadPitchDataApp_autogen
.PHONY : CSPC_UploadPitchDataApp_autogen

# fast build rule for target.
CSPC_UploadPitchDataApp_autogen/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp_autogen.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp_autogen.dir/build
.PHONY : CSPC_UploadPitchDataApp_autogen/fast

CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.obj: CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.obj
.PHONY : CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.obj

# target to build an object file
CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.obj
.PHONY : CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.obj

CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.i: CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.i
.PHONY : CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.i

# target to preprocess a source file
CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.i
.PHONY : CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.i

CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.s: CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.s
.PHONY : CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.s

# target to generate assembly for a file
CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.s
.PHONY : CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.s

CSPC_UploadPitchDataApp_autogen/mocs_compilation.obj: CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.obj
.PHONY : CSPC_UploadPitchDataApp_autogen/mocs_compilation.obj

# target to build an object file
CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.obj
.PHONY : CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.obj

CSPC_UploadPitchDataApp_autogen/mocs_compilation.i: CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.i
.PHONY : CSPC_UploadPitchDataApp_autogen/mocs_compilation.i

# target to preprocess a source file
CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.i
.PHONY : CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.i

CSPC_UploadPitchDataApp_autogen/mocs_compilation.s: CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.s
.PHONY : CSPC_UploadPitchDataApp_autogen/mocs_compilation.s

# target to generate assembly for a file
CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.s
.PHONY : CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.s

CSV/CSVReader.obj: CSV/CSVReader.cpp.obj
.PHONY : CSV/CSVReader.obj

# target to build an object file
CSV/CSVReader.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVReader.cpp.obj
.PHONY : CSV/CSVReader.cpp.obj

CSV/CSVReader.i: CSV/CSVReader.cpp.i
.PHONY : CSV/CSVReader.i

# target to preprocess a source file
CSV/CSVReader.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVReader.cpp.i
.PHONY : CSV/CSVReader.cpp.i

CSV/CSVReader.s: CSV/CSVReader.cpp.s
.PHONY : CSV/CSVReader.s

# target to generate assembly for a file
CSV/CSVReader.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVReader.cpp.s
.PHONY : CSV/CSVReader.cpp.s

CSV/CSVWriter.obj: CSV/CSVWriter.cpp.obj
.PHONY : CSV/CSVWriter.obj

# target to build an object file
CSV/CSVWriter.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVWriter.cpp.obj
.PHONY : CSV/CSVWriter.cpp.obj

CSV/CSVWriter.i: CSV/CSVWriter.cpp.i
.PHONY : CSV/CSVWriter.i

# target to preprocess a source file
CSV/CSVWriter.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVWriter.cpp.i
.PHONY : CSV/CSVWriter.cpp.i

CSV/CSVWriter.s: CSV/CSVWriter.cpp.s
.PHONY : CSV/CSVWriter.s

# target to generate assembly for a file
CSV/CSVWriter.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVWriter.cpp.s
.PHONY : CSV/CSVWriter.cpp.s

Comm.obj: Comm.cpp.obj
.PHONY : Comm.obj

# target to build an object file
Comm.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/Comm.cpp.obj
.PHONY : Comm.cpp.obj

Comm.i: Comm.cpp.i
.PHONY : Comm.i

# target to preprocess a source file
Comm.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/Comm.cpp.i
.PHONY : Comm.cpp.i

Comm.s: Comm.cpp.s
.PHONY : Comm.s

# target to generate assembly for a file
Comm.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/Comm.cpp.s
.PHONY : Comm.cpp.s

Config/ConfigLoader.obj: Config/ConfigLoader.cpp.obj
.PHONY : Config/ConfigLoader.obj

# target to build an object file
Config/ConfigLoader.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/ConfigLoader.cpp.obj
.PHONY : Config/ConfigLoader.cpp.obj

Config/ConfigLoader.i: Config/ConfigLoader.cpp.i
.PHONY : Config/ConfigLoader.i

# target to preprocess a source file
Config/ConfigLoader.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/ConfigLoader.cpp.i
.PHONY : Config/ConfigLoader.cpp.i

Config/ConfigLoader.s: Config/ConfigLoader.cpp.s
.PHONY : Config/ConfigLoader.s

# target to generate assembly for a file
Config/ConfigLoader.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/ConfigLoader.cpp.s
.PHONY : Config/ConfigLoader.cpp.s

Config/XmlConfig.obj: Config/XmlConfig.cpp.obj
.PHONY : Config/XmlConfig.obj

# target to build an object file
Config/XmlConfig.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/XmlConfig.cpp.obj
.PHONY : Config/XmlConfig.cpp.obj

Config/XmlConfig.i: Config/XmlConfig.cpp.i
.PHONY : Config/XmlConfig.i

# target to preprocess a source file
Config/XmlConfig.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/XmlConfig.cpp.i
.PHONY : Config/XmlConfig.cpp.i

Config/XmlConfig.s: Config/XmlConfig.cpp.s
.PHONY : Config/XmlConfig.s

# target to generate assembly for a file
Config/XmlConfig.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/XmlConfig.cpp.s
.PHONY : Config/XmlConfig.cpp.s

DataStore/DataStore.obj: DataStore/DataStore.cpp.obj
.PHONY : DataStore/DataStore.obj

# target to build an object file
DataStore/DataStore.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/DataStore/DataStore.cpp.obj
.PHONY : DataStore/DataStore.cpp.obj

DataStore/DataStore.i: DataStore/DataStore.cpp.i
.PHONY : DataStore/DataStore.i

# target to preprocess a source file
DataStore/DataStore.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/DataStore/DataStore.cpp.i
.PHONY : DataStore/DataStore.cpp.i

DataStore/DataStore.s: DataStore/DataStore.cpp.s
.PHONY : DataStore/DataStore.s

# target to generate assembly for a file
DataStore/DataStore.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/DataStore/DataStore.cpp.s
.PHONY : DataStore/DataStore.cpp.s

MainForm.obj: MainForm.cpp.obj
.PHONY : MainForm.obj

# target to build an object file
MainForm.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/MainForm.cpp.obj
.PHONY : MainForm.cpp.obj

MainForm.i: MainForm.cpp.i
.PHONY : MainForm.i

# target to preprocess a source file
MainForm.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/MainForm.cpp.i
.PHONY : MainForm.cpp.i

MainForm.s: MainForm.cpp.s
.PHONY : MainForm.s

# target to generate assembly for a file
MainForm.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/MainForm.cpp.s
.PHONY : MainForm.cpp.s

Pojo/MESData.obj: Pojo/MESData.cpp.obj
.PHONY : Pojo/MESData.obj

# target to build an object file
Pojo/MESData.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/MESData.cpp.obj
.PHONY : Pojo/MESData.cpp.obj

Pojo/MESData.i: Pojo/MESData.cpp.i
.PHONY : Pojo/MESData.i

# target to preprocess a source file
Pojo/MESData.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/MESData.cpp.i
.PHONY : Pojo/MESData.cpp.i

Pojo/MESData.s: Pojo/MESData.cpp.s
.PHONY : Pojo/MESData.s

# target to generate assembly for a file
Pojo/MESData.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/MESData.cpp.s
.PHONY : Pojo/MESData.cpp.s

Pojo/PitchData.obj: Pojo/PitchData.cpp.obj
.PHONY : Pojo/PitchData.obj

# target to build an object file
Pojo/PitchData.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/PitchData.cpp.obj
.PHONY : Pojo/PitchData.cpp.obj

Pojo/PitchData.i: Pojo/PitchData.cpp.i
.PHONY : Pojo/PitchData.i

# target to preprocess a source file
Pojo/PitchData.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/PitchData.cpp.i
.PHONY : Pojo/PitchData.cpp.i

Pojo/PitchData.s: Pojo/PitchData.cpp.s
.PHONY : Pojo/PitchData.s

# target to generate assembly for a file
Pojo/PitchData.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/PitchData.cpp.s
.PHONY : Pojo/PitchData.cpp.s

main.obj: main.cpp.obj
.PHONY : main.obj

# target to build an object file
main.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/main.cpp.obj
.PHONY : main.cpp.obj

main.i: main.cpp.i
.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s
.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/main.cpp.s
.PHONY : main.cpp.s

version.obj: version.rc.obj
.PHONY : version.obj

# target to build an object file
version.rc.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/version.rc.obj
.PHONY : version.rc.obj

widget.obj: widget.cpp.obj
.PHONY : widget.obj

# target to build an object file
widget.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/widget.cpp.obj
.PHONY : widget.cpp.obj

widget.i: widget.cpp.i
.PHONY : widget.i

# target to preprocess a source file
widget.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/widget.cpp.i
.PHONY : widget.cpp.i

widget.s: widget.cpp.s
.PHONY : widget.s

# target to generate assembly for a file
widget.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/widget.cpp.s
.PHONY : widget.cpp.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... rebuild_cache
	@echo ... CSPC_UploadPitchDataApp_autogen
	@echo ... CSPC_UploadPitchDataApp
	@echo ... CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.obj
	@echo ... CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.i
	@echo ... CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.s
	@echo ... CSPC_UploadPitchDataApp_autogen/mocs_compilation.obj
	@echo ... CSPC_UploadPitchDataApp_autogen/mocs_compilation.i
	@echo ... CSPC_UploadPitchDataApp_autogen/mocs_compilation.s
	@echo ... CSV/CSVReader.obj
	@echo ... CSV/CSVReader.i
	@echo ... CSV/CSVReader.s
	@echo ... CSV/CSVWriter.obj
	@echo ... CSV/CSVWriter.i
	@echo ... CSV/CSVWriter.s
	@echo ... Comm.obj
	@echo ... Comm.i
	@echo ... Comm.s
	@echo ... Config/ConfigLoader.obj
	@echo ... Config/ConfigLoader.i
	@echo ... Config/ConfigLoader.s
	@echo ... Config/XmlConfig.obj
	@echo ... Config/XmlConfig.i
	@echo ... Config/XmlConfig.s
	@echo ... DataStore/DataStore.obj
	@echo ... DataStore/DataStore.i
	@echo ... DataStore/DataStore.s
	@echo ... MainForm.obj
	@echo ... MainForm.i
	@echo ... MainForm.s
	@echo ... Pojo/MESData.obj
	@echo ... Pojo/MESData.i
	@echo ... Pojo/MESData.s
	@echo ... Pojo/PitchData.obj
	@echo ... Pojo/PitchData.i
	@echo ... Pojo/PitchData.s
	@echo ... main.obj
	@echo ... main.i
	@echo ... main.s
	@echo ... version.obj
	@echo ... widget.obj
	@echo ... widget.i
	@echo ... widget.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

