#include "CSVWriter.h"

#include <QDebug>
#include <QDir>


CSVWriter::CSVWriter(QObject *parent) : QObject(parent) {
}

void CSVWriter::writeMesInfoErrorData(const QVector<PitchData> &mesInfoErrorData, const QString &time, const CSVFileType flag) {
    //负责输出未通过前置检测的数据 包括固件版本异常的 找到主板标签 底板 主板标签信息有异常的

    QString path;
    if (CSVFileType::FirmewareVersionErr == flag) {
        path = QDir::currentPath() + "/固件版本异常数据表/";
        path += "固件版本异常数据表" + time + ".csv";
    } else if (CSVFileType::MESInfoErr == flag) {
        path = QDir::currentPath() + "/mes检测异常数据表/";
        path += "mes检测异常数据表" + time + ".csv";
    }

    qDebug() << path;

    QDir dir = QFileInfo(path).dir();
    if (!dir.exists() && !dir.mkpath(dir.absolutePath())) {
        qDebug() << "无法创建文件夹";
        return;
    }

    QFile file(path);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qDebug() << "无法创建文件";
        return;
    }

    QTextStream out(&file);
    //    out.setCodec("GBK");
    //写入标题行
    //    out << "电机标签" << ',' << "MCUID" << ',' << "固件版本" << ',' << "测试结果" << ','
    //        << "最小俯仰角" << ',' << "最大俯仰角" << ',' << "俯仰角下限" << ',' << "俯仰角上限" << "\n";

    out << "Nbr" << ',' << "MCUID" << ',' << "FirmwareVersion" << ',' << "TestResult" << ',' << "PitchAngleValueMin" << ',' << "PitchAngleValueMax" << ','
        << "PitchAngleValueStandardMin" << ',' << "PitchAngleValueStandardMax"
        << "\n";

    for (const PitchData &item : mesInfoErrorData) {
        out << item.getNbr() << ',' << item.getMcuID() << ',' << item.getFirmwareVersion() << ',' << item.getTestResult() << ',' << item.getPitchAngleValueMin()
            << ',' << item.getPitchAngleValueMax() << ',' << item.getPitchAngleValueStandardMin() << ',' << item.getPitchAngleValueStandardMax() << "\n";
    }
}

void CSVWriter::writeMesData(const QVector<MESData> &mesData, const QString &time, const CSVFileType flag) {
    QString path;
    if (CSVFileType::MesUploadSuccess == flag) {
        path = QDir::currentPath() + "/mes上传成功数据表/";
        path += "mes上传成功数据表" + time + ".csv";
    } else if (CSVFileType::MESUploadFailed == flag) {
        path = QDir::currentPath() + "/mes上传失败数据表/";
        path += "mes上传失败数据表" + time + ".csv";
    }

    qDebug() << path;

    QDir dir = QFileInfo(path).dir();
    if (!dir.exists() && !dir.mkpath(dir.absolutePath())) {
        qDebug() << "无法创建文件夹";
        return;
    }

    QFile file(path);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qDebug() << "无法创建文件";
        return;
    }

    QTextStream out(&file);
    //    out.setCodec("UTF-8");
    //写入标题行
    //    out << "域名" << ',' << "事务号" << ',' << "电机标签" << ',' << "SN码" << ',' << "工序" << ','
    //        << "员工账号" << ',' << "日期" << ',' << "时间" << ',' << "测试结果" << ','
    //        << "工位" << ',' << "主板标签" << ','<< "工单" << ','<< "MCUID" << ','
    //        << "项目" << ',' << "标准值" << ','<< "测试值" << ','<< "是否合格" << ','
    //        << "项目" << ','<< "标准值" << ','<< "测试值" << ','<< "是否合格" << "\n";

    out << "domain" << ',' << "trnbr" << ',' << "nbr" << ',' << "SNCode" << ',' << "op" << ',' << "userID" << ',' << "date" << ',' << "time" << ',' << "rsnCode"
        << ',' << "station" << ',' << "topNbr" << ',' << "workOrder" << ',' << "MCUID" << ',' << "projrct1" << ',' << "stand1" << ',' << "act1" << ','
        << "hege1" << ',' << "projrct2" << ',' << "stand2" << ',' << "act2" << ',' << "hege2"
        << "\n";

    for (const MESData &item : mesData) {
        out << item.domain << ',' << item.trnbr << ',' << item.nbr << ',' << item.SNCode << ',' << item.op << ',' << item.userID << ',' << item.date << ','
            << item.time << ',' << item.rsnCode << ',' << item.station << ',' << item.topNbr << ',' << item.workOrder << ',' << item.mcuID << ','
            << item.projrct1 << ',' << item.stand1 << ',' << item.act1 << ',' << item.hege1 << ',' << item.projrct2 << ',' << item.stand2 << ',' << item.act2
            << ',' << item.hege2 << "\n";
    }
}
