D:\Programs\CMake\bin\cmake.exe -E rm -f CMakeFiles\CSPC_UploadPitchDataApp.dir/objects.a
D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\ar.exe qc CMakeFiles\CSPC_UploadPitchDataApp.dir/objects.a @CMakeFiles\CSPC_UploadPitchDataApp.dir\objects1.rsp
D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe  -finput-charset=UTF-8 -Os -DNDEBUG -Wl,--whole-archive CMakeFiles\CSPC_UploadPitchDataApp.dir/objects.a -Wl,--no-whole-archive -o bin\CSPC_UploadPitchDataApp.exe -Wl,--out-implib,lib\libCSPC_UploadPitchDataApp.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\CSPC_UploadPitchDataApp.dir\linklibs.rsp
