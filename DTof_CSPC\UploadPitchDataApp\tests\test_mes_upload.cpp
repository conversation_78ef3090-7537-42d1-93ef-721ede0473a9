#include <QCoreApplication>
#include <QDebug>
#include <QDir>
#include <QFileInfo>
#include "../CSV/CSVReader.h"
#include "../Pojo/PitchData.h"

/**
 * MES数据上传功能测试程序
 * 
 * 测试内容：
 * 1. CSV文件读取和状态处理
 * 2. 工作拷贝创建
 * 3. 重复数据去重
 * 4. 状态更新功能
 * 5. 真正需要上传数据统计
 */

class MESUploadTester {
public:
    MESUploadTester() {
        // 设置测试数据路径
        testDataPath = "../build/bin/俯仰角视觉检测_195_20250623143833252.csv";
        testOutputDir = "./test_output/";
        
        // 创建输出目录
        QDir().mkpath(testOutputDir);
    }
    
    void runAllTests() {
        qDebug() << "=== MES数据上传功能测试开始 ===";
        
        test1_ReadOriginalCSV();
        test2_CreateWorkingCopy();
        test3_ReadDataWithStatus();
        test4_RemoveDuplicates();
        test5_FilterNeedsProcessing();
        test6_GetActualUploadData();
        test7_UpdateUploadStatus();
        
        qDebug() << "=== 所有测试完成 ===";
    }

private:
    QString testDataPath;
    QString testOutputDir;
    CSVReader csvReader;
    
    // 测试1：读取原始CSV文件
    void test1_ReadOriginalCSV() {
        qDebug() << "\n--- 测试1：读取原始CSV文件 ---";
        
        QFileInfo fileInfo(testDataPath);
        if (!fileInfo.exists()) {
            qDebug() << "错误：测试数据文件不存在:" << testDataPath;
            return;
        }
        
        QVector<PitchData> data = csvReader.readDataFromCsv(testDataPath);
        qDebug() << "原始CSV文件读取结果:";
        qDebug() << "- 文件路径:" << testDataPath;
        qDebug() << "- 数据条数:" << data.size();
        
        if (!data.isEmpty()) {
            qDebug() << "- 第一条数据:";
            qDebug() << "  电机标签:" << data[0].getNbr();
            qDebug() << "  MCU ID:" << data[0].getMcuID();
            qDebug() << "  固件版本:" << data[0].getFirmwareVersion();
            qDebug() << "  录入状态:" << static_cast<int>(data[0].getUploadStatus());
        }
    }
    
    // 测试2：创建工作拷贝
    void test2_CreateWorkingCopy() {
        qDebug() << "\n--- 测试2：创建工作拷贝 ---";
        
        bool isWorkingCopy = csvReader.isWorkingCopy(testDataPath);
        qDebug() << "原始文件是否为工作拷贝:" << isWorkingCopy;
        
        QString workingCopyPath = csvReader.createWorkingCopy(testDataPath);
        qDebug() << "工作拷贝创建结果:";
        qDebug() << "- 工作拷贝路径:" << workingCopyPath;
        
        if (!workingCopyPath.isEmpty()) {
            bool isNewWorkingCopy = csvReader.isWorkingCopy(workingCopyPath);
            qDebug() << "- 新文件是否为工作拷贝:" << isNewWorkingCopy;
        }
    }
    
    // 测试3：读取包含状态的数据
    void test3_ReadDataWithStatus() {
        qDebug() << "\n--- 测试3：读取包含状态的数据 ---";
        
        QVector<PitchData> data = csvReader.readDataWithStatus(testDataPath);
        qDebug() << "包含状态的数据读取结果:";
        qDebug() << "- 数据条数:" << data.size();
        
        // 统计各种状态的数量
        int notUploaded = 0, uploaded = 0, failed = 0;
        for (const PitchData& item : data) {
            switch (item.getUploadStatus()) {
                case UploadStatus::NotUploaded: notUploaded++; break;
                case UploadStatus::Uploaded: uploaded++; break;
                case UploadStatus::Failed: failed++; break;
            }
        }
        
        qDebug() << "- 状态统计:";
        qDebug() << "  未录入:" << notUploaded;
        qDebug() << "  已录入:" << uploaded;
        qDebug() << "  录入失败:" << failed;
    }
    
    // 测试4：去重功能
    void test4_RemoveDuplicates() {
        qDebug() << "\n--- 测试4：去重功能测试 ---";
        
        QVector<PitchData> allData = csvReader.readDataWithStatus(testDataPath);
        QVector<PitchData> uniqueData = csvReader.removeDuplicatesKeepLatest(allData);
        
        qDebug() << "去重处理结果:";
        qDebug() << "- 原始数据:" << allData.size();
        qDebug() << "- 去重后:" << uniqueData.size();
        qDebug() << "- 去重比例:" << QString::number((1.0 - (double)uniqueData.size() / allData.size()) * 100, 'f', 1) << "%";
        
        // 检查是否有重复的电机标签
        QSet<QString> nbrSet;
        bool hasDuplicates = false;
        for (const PitchData& item : uniqueData) {
            if (nbrSet.contains(item.getNbr())) {
                hasDuplicates = true;
                break;
            }
            nbrSet.insert(item.getNbr());
        }
        qDebug() << "- 去重后是否还有重复:" << hasDuplicates;
    }
    
    // 测试5：过滤需要处理的数据
    void test5_FilterNeedsProcessing() {
        qDebug() << "\n--- 测试5：过滤需要处理的数据 ---";
        
        QVector<PitchData> allData = csvReader.readDataWithStatus(testDataPath);
        QVector<PitchData> needsProcessing = csvReader.filterNeedsProcessing(allData);
        
        qDebug() << "过滤需要处理数据结果:";
        qDebug() << "- 全部数据:" << allData.size();
        qDebug() << "- 需要处理:" << needsProcessing.size();
        qDebug() << "- 需要处理比例:" << QString::number((double)needsProcessing.size() / allData.size() * 100, 'f', 1) << "%";
    }
    
    // 测试6：获取真正需要上传的数据
    void test6_GetActualUploadData() {
        qDebug() << "\n--- 测试6：获取真正需要上传的数据 ---";
        
        QVector<PitchData> allData = csvReader.readDataWithStatus(testDataPath);
        QVector<PitchData> actualUploadData = csvReader.getActualUploadData(allData);
        
        qDebug() << "真正需要上传数据统计:";
        qDebug() << "- 原始数据:" << allData.size();
        qDebug() << "- 需要上传:" << actualUploadData.size();
        qDebug() << "- 上传比例:" << QString::number((double)actualUploadData.size() / allData.size() * 100, 'f', 1) << "%";
        
        // 显示前5条需要上传的数据
        qDebug() << "- 前5条需要上传的数据:";
        for (int i = 0; i < qMin(5, actualUploadData.size()); i++) {
            const PitchData& item = actualUploadData[i];
            qDebug() << QString("  %1. 电机标签:%2, 状态:%3")
                        .arg(i+1)
                        .arg(item.getNbr())
                        .arg(static_cast<int>(item.getUploadStatus()));
        }
    }
    
    // 测试7：状态更新功能
    void test7_UpdateUploadStatus() {
        qDebug() << "\n--- 测试7：状态更新功能测试 ---";
        
        // 创建测试用的工作拷贝
        QString workingCopyPath = csvReader.getWorkingCopyPath(testDataPath);
        if (workingCopyPath.isEmpty()) {
            qDebug() << "错误：无法创建工作拷贝";
            return;
        }
        
        QVector<PitchData> data = csvReader.readDataFromCsv(workingCopyPath);
        if (data.isEmpty()) {
            qDebug() << "错误：工作拷贝中没有数据";
            return;
        }
        
        // 测试按电机标签更新状态
        QString testNbr = data[0].getNbr();
        qDebug() << "测试更新电机标签:" << testNbr;
        
        bool updateResult = csvReader.updateUploadStatusByNbr(workingCopyPath, testNbr, UploadStatus::Uploaded);
        qDebug() << "状态更新结果:" << updateResult;
        
        // 验证更新结果
        QVector<PitchData> updatedData = csvReader.readDataFromCsv(workingCopyPath);
        for (const PitchData& item : updatedData) {
            if (item.getNbr() == testNbr) {
                qDebug() << "验证更新结果 - 电机标签:" << testNbr << "新状态:" << static_cast<int>(item.getUploadStatus());
                break;
            }
        }
    }
};

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    MESUploadTester tester;
    tester.runAllTests();
    
    return 0;
}
