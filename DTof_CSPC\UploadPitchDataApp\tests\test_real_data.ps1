# Real MES Data Test Script (Simplified)
Write-Host "Real MES Data Integration Test" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Cyan

$AppDir = "..\build\bin"
$RealDataFile = "$AppDir\俯仰角视觉检测_195_20250623143833252.csv"
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"

Write-Host "App Directory: $AppDir"
Write-Host "Real Data File: $RealDataFile"
Write-Host "Timestamp: $timestamp"
Write-Host ""

# Check if real data file exists
if (-not (Test-Path $RealDataFile)) {
    Write-Host "ERROR: Real data file not found!" -ForegroundColor Red
    exit 1
}

$fileInfo = Get-Item $RealDataFile
Write-Host "Real data file found:" -ForegroundColor Green
Write-Host "  Size: $($fileInfo.Length) bytes"
Write-Host "  Modified: $($fileInfo.LastWriteTime)"
Write-Host ""

Push-Location $AppDir

# Analyze the real CSV data
Write-Host "Analyzing real CSV data..." -ForegroundColor Yellow
$content = Get-Content $RealDataFile -Encoding UTF8
$totalLines = $content.Count
Write-Host "Total lines: $totalLines"

if ($totalLines -gt 0) {
    $headerLine = $content[0]
    Write-Host "Header: $headerLine"
    
    $fields = $headerLine.Split(',')
    Write-Host "Columns: $($fields.Count)"
}

# Analyze data rows (skip header and empty lines)
$dataRows = $content | Select-Object -Skip 1 | Where-Object { $_.Trim() -ne "" }
$dataCount = $dataRows.Count
Write-Host "Data rows: $dataCount"

if ($dataCount -gt 0) {
    Write-Host ""
    Write-Host "Sample data analysis:" -ForegroundColor Yellow
    
    # Analyze motor labels for duplicates
    $motorLabels = @()
    $testResults = @()
    
    foreach ($row in $dataRows) {
        $fields = $row.Split(',')
        if ($fields.Count -ge 5) {
            $motorLabels += $fields[1]  # Motor label
            $testResults += $fields[4]  # Test result
        }
    }
    
    $uniqueMotorLabels = $motorLabels | Sort-Object | Get-Unique
    
    Write-Host "Unique motor labels: $($uniqueMotorLabels.Count)"
    Write-Host "Duplicate motor entries: $(($motorLabels.Count - $uniqueMotorLabels.Count))"
    
    # Show motor label frequency
    Write-Host ""
    Write-Host "Motor label frequency:"
    $motorLabels | Group-Object | Sort-Object Count -Descending | ForEach-Object {
        Write-Host "  $($_.Name): $($_.Count) times"
    }
    
    # Show test results
    $passCount = ($testResults | Where-Object { $_ -eq "1" }).Count
    $failCount = ($testResults | Where-Object { $_ -eq "0" }).Count
    Write-Host ""
    Write-Host "Test results:"
    Write-Host "  Pass (1): $passCount"
    Write-Host "  Fail (0): $failCount"
}

Write-Host ""
Write-Host "Creating working copy..." -ForegroundColor Yellow

# Create working copy with status column
$workingCopyName = "pitch_test_working_copy_$timestamp.csv"

try {
    # Read original content and add status column
    $originalContent = Get-Content $RealDataFile -Encoding UTF8
    $workingContent = @()
    
    # Add status column to header
    if ($originalContent.Count -gt 0) {
        $headerWithStatus = $originalContent[0] + ",Upload_Status"
        $workingContent += $headerWithStatus
        
        # Add empty status column to data rows
        for ($i = 1; $i -lt $originalContent.Count; $i++) {
            if ($originalContent[$i].Trim() -ne "") {
                $workingContent += $originalContent[$i] + ","
            }
        }
    }
    
    # Save working copy
    $workingContent | Set-Content $workingCopyName -Encoding UTF8
    
    if (Test-Path $workingCopyName) {
        Write-Host "Working copy created: $workingCopyName" -ForegroundColor Green
        $workingFileInfo = Get-Item $workingCopyName
        Write-Host "  Size: $($workingFileInfo.Length) bytes"
        
        # Verify working copy content
        $workingCopyContent = Get-Content $workingCopyName -Encoding UTF8
        Write-Host "  Lines: $($workingCopyContent.Count)"
        Write-Host "  Header: $($workingCopyContent[0])"
    }
} catch {
    Write-Host "Working copy creation failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Simulating status updates..." -ForegroundColor Yellow

if (Test-Path $workingCopyName) {
    # Read working copy and update statuses
    $workingLines = Get-Content $workingCopyName -Encoding UTF8
    
    # Update some rows with different statuses
    for ($i = 1; $i -lt $workingLines.Count; $i++) {
        if ($workingLines[$i].Trim() -ne "") {
            $fields = $workingLines[$i].Split(',')
            if ($fields.Count -ge 10) {
                # Simulate different upload statuses
                switch ($i % 3) {
                    1 { $fields[-1] = "1" }    # Uploaded
                    2 { $fields[-1] = "" }     # Not uploaded
                    0 { $fields[-1] = "-1" }   # Failed
                }
                $workingLines[$i] = $fields -join ","
            }
        }
    }
    
    # Save updated working copy
    $updatedWorkingCopy = "pitch_test_status_updated_$timestamp.csv"
    $workingLines | Set-Content $updatedWorkingCopy -Encoding UTF8
    
    if (Test-Path $updatedWorkingCopy) {
        Write-Host "Status updated copy created: $updatedWorkingCopy" -ForegroundColor Green
        
        # Analyze status distribution
        $statusLines = Get-Content $updatedWorkingCopy -Encoding UTF8 | Select-Object -Skip 1
        $uploadedCount = 0
        $notUploadedCount = 0
        $failedCount = 0
        
        foreach ($line in $statusLines) {
            if ($line.Trim() -ne "") {
                $fields = $line.Split(',')
                if ($fields.Count -ge 10) {
                    $status = $fields[-1].Trim()
                    switch ($status) {
                        "1" { $uploadedCount++ }
                        "" { $notUploadedCount++ }
                        "-1" { $failedCount++ }
                    }
                }
            }
        }
        
        Write-Host ""
        Write-Host "Status distribution simulation:"
        Write-Host "  Uploaded (1): $uploadedCount"
        Write-Host "  Not uploaded (empty): $notUploadedCount"
        Write-Host "  Failed (-1): $failedCount"
        Write-Host "  Total: $(($uploadedCount + $notUploadedCount + $failedCount))"
        
        # Calculate optimization
        $needsUpload = $notUploadedCount + $failedCount
        $reductionRate = if ($dataCount -gt 0) { (1 - ($needsUpload / $dataCount)) * 100 } else { 0 }
        Write-Host ""
        Write-Host "Upload optimization:"
        Write-Host "  Original data: $dataCount rows"
        Write-Host "  Needs upload: $needsUpload rows"
        Write-Host "  Reduction rate: $([math]::Round($reductionRate, 2))%"
    }
}

Write-Host ""
Write-Host "Generated files:" -ForegroundColor Yellow
$generatedFiles = Get-ChildItem "pitch_test_*" -ErrorAction SilentlyContinue
if ($generatedFiles) {
    foreach ($file in $generatedFiles) {
        Write-Host "  $($file.Name) ($($file.Length) bytes)"
    }
} else {
    Write-Host "  No generated files found"
}

Pop-Location

Write-Host ""
Write-Host "Test Summary:" -ForegroundColor Green
Write-Host "- Real CSV file analyzed: $dataCount data rows"
Write-Host "- Working copy created with status column"
Write-Host "- Status simulation completed"
Write-Host "- Duplicate detection: $($motorLabels.Count - $uniqueMotorLabels.Count) duplicates found"

Write-Host ""
Write-Host "Test completed successfully!" -ForegroundColor Green
