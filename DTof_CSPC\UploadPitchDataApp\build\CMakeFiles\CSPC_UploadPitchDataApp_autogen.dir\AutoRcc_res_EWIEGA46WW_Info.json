{"BUILD_DIR": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build/CSPC_UploadPitchDataApp_autogen", "CMAKE_BINARY_DIR": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build", "CMAKE_CURRENT_BINARY_DIR": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build", "CMAKE_CURRENT_SOURCE_DIR": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp", "CMAKE_SOURCE_DIR": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp", "INCLUDE_DIR": "F:/13_Ya<PERSON>-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build/CSPC_UploadPitchDataApp_autogen/include", "INPUTS": ["F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/resource/cspc.ico", "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/resource/cspc.jpg"], "LOCK_FILE": "F:/13_Ya<PERSON>-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build/CMakeFiles/CSPC_UploadPitchDataApp_autogen.dir/AutoRcc_res_EWIEGA46WW_Lock.lock", "MULTI_CONFIG": false, "OPTIONS": ["-name", "res"], "OUTPUT_CHECKSUM": "EWIEGA46WW", "OUTPUT_NAME": "qrc_res.cpp", "RCC_EXECUTABLE": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/bin/rcc.exe", "RCC_LIST_OPTIONS": ["--list"], "SETTINGS_FILE": "F:/13_Ya<PERSON>-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build/CMakeFiles/CSPC_UploadPitchDataApp_autogen.dir/AutoRcc_res_EWIEGA46WW_Used.txt", "SOURCE": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/res.qrc", "VERBOSITY": 0}