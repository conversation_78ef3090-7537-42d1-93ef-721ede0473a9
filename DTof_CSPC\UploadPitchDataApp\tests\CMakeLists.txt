cmake_minimum_required(VERSION 3.16)
project(MESUploadTest)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt5
find_package(Qt5 REQUIRED COMPONENTS Core Widgets Sql)

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/..)

# 源文件
set(SOURCES
    test_mes_upload.cpp
    ../CSV/CSVReader.cpp
    ../Pojo/PitchData.cpp
)

# 头文件
set(HEADERS
    ../CSV/CSVReader.h
    ../Pojo/PitchData.h
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# 链接Qt库
target_link_libraries(${PROJECT_NAME} 
    Qt5::Core 
    Qt5::Widgets 
    Qt5::Sql
)

# 设置输出目录
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/bin
)

# 编译选项
if(WIN32)
    target_compile_definitions(${PROJECT_NAME} PRIVATE WIN32_LEAN_AND_MEAN)
endif()
