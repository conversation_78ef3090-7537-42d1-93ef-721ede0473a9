cmake_minimum_required(VERSION 3.16)
project(MESTests)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt5
find_package(Qt5 REQUIRED COMPONENTS Core Widgets Sql)

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/..)

# 简单逻辑测试（不依赖Qt）
add_executable(LogicTest
    test_logic.cpp
)

# 基础功能测试
set(BASIC_TEST_SOURCES
    test_mes_upload.cpp
    ../CSV/CSVReader.cpp
    ../Pojo/PitchData.cpp
)

add_executable(BasicTest ${BASIC_TEST_SOURCES})
target_link_libraries(BasicTest Qt5::Core Qt5::Widgets Qt5::Sql)

# 集成测试（包含Widget）
set(INTEGRATION_TEST_SOURCES
    test_mes_integration.cpp
    ../widget.cpp
    ../CSV/CSVReader.cpp
    ../CSV/CSVWriter.cpp
    ../Pojo/PitchData.cpp
    ../Pojo/MESData.cpp
    ../Config/ConfigLoader.cpp
    ../DataStore/DataStore.cpp
)

add_executable(IntegrationTest ${INTEGRATION_TEST_SOURCES})
target_link_libraries(IntegrationTest Qt5::Core Qt5::Widgets Qt5::Sql)

# 设置输出目录
set_target_properties(LogicTest BasicTest IntegrationTest PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/bin
)

# 编译选项
if(WIN32)
    target_compile_definitions(BasicTest PRIVATE WIN32_LEAN_AND_MEAN)
    target_compile_definitions(IntegrationTest PRIVATE WIN32_LEAN_AND_MEAN)
endif()
