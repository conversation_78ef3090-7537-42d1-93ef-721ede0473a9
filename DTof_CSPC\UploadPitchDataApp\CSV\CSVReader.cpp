#include "CSVReader.h"

#include <QDebug>
#include <QFile>
#include <QTextStream>


CSVReader::CSVReader(QObject *parent) : QObject(parent) {
}

QVector<PitchData> CSVReader::readDataFromCsv(const QString &fileName) {
    QVector<PitchData> pitchData;

    QFile file(fileName);
    if (!file.open(QIODevice::ReadOnly)) {
        return pitchData;
    }

    QTextStream in(&file);
    while (!in.atEnd()) {
        QString     line = in.readLine();
        QString     lineTmp{line};
        QStringList fields = lineTmp.split(",");
        if (fields.size() < 8) {
            qDebug() << "CSV data col error";
            continue;
        }

        QString nbr                          = fields[0].rightJustified(8, '0', false);
        QString mcuID                        = fields[1];
        QString firmwareVersion              = fields[2];
        bool    testResultOk                 = false;
        int     testResult                   = fields[3].toInt(&testResultOk);
        bool    pitchAngleValueMinOk         = false;
        float   pitchAngleValueMin           = fields[4].toFloat(&pitchAngleValueMinOk);
        bool    pitchAngleValueMaxOk         = false;
        float   pitchAngleValueMax           = fields[5].toFloat(&pitchAngleValueMaxOk);
        bool    pitchAngleValueStandardMinOk = false;
        float   pitchAngleValueStandardMin   = fields[6].toFloat(&pitchAngleValueStandardMinOk);
        bool    pitchAngleValueStandardMaxOk = false;
        float   pitchAngleValueStandardMax   = fields[7].toFloat(&pitchAngleValueStandardMaxOk);

        //各个数据能够正确转化为数值的话
        if (!(testResultOk && pitchAngleValueMinOk && pitchAngleValueMaxOk && pitchAngleValueStandardMinOk && pitchAngleValueStandardMaxOk)) {
            qDebug() << "CSV data error";
            continue;
        }
        PitchData tmpData(
            nbr, mcuID, firmwareVersion, testResult, pitchAngleValueMin, pitchAngleValueMax, pitchAngleValueStandardMin, pitchAngleValueStandardMax);
        pitchData.push_back(tmpData);
    }

    return pitchData;
}
