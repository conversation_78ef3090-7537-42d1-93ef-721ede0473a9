#include "CSVReader.h"

#include <QDebug>
#include <QFile>
#include <QSet>
#include <QTextStream>



CSVReader::CSVReader(QObject *parent) : QObject(parent) {
}

QVector<PitchData> CSVReader::readDataFromCsv(const QString &fileName) {
    QVector<PitchData> pitchData;

    QFile file(fileName);
    if (!file.open(QIODevice::ReadOnly)) {
        return pitchData;
    }

    QTextStream in(&file);
    while (!in.atEnd()) {
        QString     line = in.readLine();
        QString     lineTmp{line};
        QStringList fields = lineTmp.split(",");
        if (fields.size() < 8) {
            qDebug() << "CSV data col error";
            continue;
        }

        QString nbr                          = fields[0].rightJustified(8, '0', false);
        QString mcuID                        = fields[1];
        QString firmwareVersion              = fields[2];
        bool    testResultOk                 = false;
        int     testResult                   = fields[3].toInt(&testResultOk);
        bool    pitchAngleValueMinOk         = false;
        float   pitchAngleValueMin           = fields[4].toFloat(&pitchAngleValueMinOk);
        bool    pitchAngleValueMaxOk         = false;
        float   pitchAngleValueMax           = fields[5].toFloat(&pitchAngleValueMaxOk);
        bool    pitchAngleValueStandardMinOk = false;
        float   pitchAngleValueStandardMin   = fields[6].toFloat(&pitchAngleValueStandardMinOk);
        bool    pitchAngleValueStandardMaxOk = false;
        float   pitchAngleValueStandardMax   = fields[7].toFloat(&pitchAngleValueStandardMaxOk);

        //各个数据能够正确转化为数值的话
        if (!(testResultOk && pitchAngleValueMinOk && pitchAngleValueMaxOk && pitchAngleValueStandardMinOk && pitchAngleValueStandardMaxOk)) {
            qDebug() << "CSV data error";
            continue;
        }
        // 检查是否有录入状态列（第9列）
        UploadStatus uploadStatus = UploadStatus::NotUploaded;
        int          rowIndex     = pitchData.size();  // 当前行索引

        if (fields.size() > 8) {
            uploadStatus = parseUploadStatus(fields[8]);
        }

        PitchData tmpData(nbr,
                          mcuID,
                          firmwareVersion,
                          testResult,
                          pitchAngleValueMin,
                          pitchAngleValueMax,
                          pitchAngleValueStandardMin,
                          pitchAngleValueStandardMax,
                          uploadStatus,
                          rowIndex);
        pitchData.push_back(tmpData);
    }

    return pitchData;
}

// 检查文件是否为工作拷贝（包含录入状态列）
bool CSVReader::isWorkingCopy(const QString &fileName) {
    QFile file(fileName);
    if (!file.open(QIODevice::ReadOnly)) {
        qDebug() << "无法打开文件进行工作拷贝检查:" << fileName;
        return false;
    }

    QTextStream in(&file);
    if (in.atEnd()) {
        return false;
    }

    QString     firstLine = in.readLine();
    QStringList fields    = firstLine.split(",");

    // 检查是否有第9列（录入状态列）
    bool hasStatusColumn = fields.size() > 8;
    qDebug() << "文件" << fileName << "工作拷贝检查结果:" << hasStatusColumn;

    return hasStatusColumn;
}

// 创建工作拷贝文件
QString CSVReader::createWorkingCopy(const QString &originalFile) {
    QFileInfo fileInfo(originalFile);
    QString   timestamp       = QDateTime::currentDateTime().toString("yyyyMMddHHmmss");
    QString   workingCopyName = QString("%1_工作副本_%2.%3").arg(fileInfo.baseName()).arg(timestamp).arg(fileInfo.suffix());

    QString workingCopyPath = fileInfo.path() + "/" + workingCopyName;

    qDebug() << "创建工作拷贝:" << originalFile << "->" << workingCopyPath;

    // 读取原始文件内容
    QStringList lines = readAllLines(originalFile);
    if (lines.isEmpty()) {
        qDebug() << "原始文件为空或读取失败:" << originalFile;
        return QString();
    }

    // 为每行添加录入状态列（初始为空）
    for (int i = 0; i < lines.size(); i++) {
        lines[i] += ",";  // 添加空的录入状态列
    }

    // 写入工作拷贝文件
    if (!writeAllLines(workingCopyPath, lines)) {
        qDebug() << "创建工作拷贝失败:" << workingCopyPath;
        return QString();
    }

    emit workingCopyCreated(workingCopyPath);
    qDebug() << "工作拷贝创建成功:" << workingCopyPath;

    return workingCopyPath;
}

// 获取工作拷贝路径（如果不存在则创建）
QString CSVReader::getWorkingCopyPath(const QString &originalFile) {
    if (isWorkingCopy(originalFile)) {
        return originalFile;  // 已经是工作拷贝
    }

    return createWorkingCopy(originalFile);
}

// 读取数据并包含状态信息
QVector<PitchData> CSVReader::readDataWithStatus(const QString &fileName) {
    QString workingCopyPath = getWorkingCopyPath(fileName);
    if (workingCopyPath.isEmpty()) {
        qDebug() << "无法获取或创建工作拷贝:" << fileName;
        return QVector<PitchData>();
    }

    return readDataFromCsv(workingCopyPath);
}

// 根据行索引更新录入状态
bool CSVReader::updateUploadStatusByRow(const QString &fileName, int rowIndex, UploadStatus status) {
    QStringList lines = readAllLines(fileName);
    if (lines.isEmpty() || rowIndex >= lines.size()) {
        qDebug() << "更新状态失败：行索引超出范围" << rowIndex << "文件行数:" << lines.size();
        return false;
    }

    QStringList fields = lines[rowIndex].split(",");

    // 确保有足够的列
    while (fields.size() < 9) {
        fields.append("");
    }

    // 更新录入状态列（第9列，索引8）
    fields[8]       = uploadStatusToString(status);
    lines[rowIndex] = fields.join(",");

    bool success = writeAllLines(fileName, lines);
    if (success) {
        qDebug() << "状态更新成功 - 行:" << rowIndex << "状态:" << static_cast<int>(status);
    } else {
        qDebug() << "状态更新失败 - 行:" << rowIndex;
    }

    return success;
}

// 根据电机标签更新录入状态（更新最新的一笔数据）
bool CSVReader::updateUploadStatusByNbr(const QString &fileName, const QString &nbr, UploadStatus status) {
    QStringList lines = readAllLines(fileName);
    if (lines.isEmpty()) {
        return false;
    }

    QString targetNbr      = nbr.rightJustified(8, '0', false);
    int     lastMatchIndex = -1;

    // 找到该电机标签的最后一笔数据（最新数据）
    for (int i = 0; i < lines.size(); i++) {
        QStringList fields = lines[i].split(",");
        if (fields.size() > 0) {
            QString currentNbr = fields[0].rightJustified(8, '0', false);
            if (currentNbr == targetNbr) {
                lastMatchIndex = i;  // 记录最后一次匹配的索引
            }
        }
    }

    // 如果找到匹配的数据，更新最新的一笔
    if (lastMatchIndex >= 0) {
        QStringList fields = lines[lastMatchIndex].split(",");

        // 确保有足够的列
        while (fields.size() < 9) {
            fields.append("");
        }

        fields[8]             = uploadStatusToString(status);
        lines[lastMatchIndex] = fields.join(",");

        bool success = writeAllLines(fileName, lines);
        if (success) {
            emit statusUpdated(nbr, status);
            qDebug() << "状态更新成功 - 电机标签:" << nbr << "行:" << lastMatchIndex << "状态:" << static_cast<int>(status);
        }
        return success;
    }

    qDebug() << "未找到电机标签:" << nbr;
    return false;
}

// 过滤需要处理的数据（未录入或异常的数据）
QVector<PitchData> CSVReader::filterNeedsProcessing(const QVector<PitchData> &data) {
    QVector<PitchData> result;

    for (const PitchData &item : data) {
        if (item.needsProcessing()) {
            result.append(item);
        }
    }

    qDebug() << "过滤需要处理的数据 - 总数:" << data.size() << "需要处理:" << result.size();
    return result;
}

// 去重并保留最新数据（逆序处理）
QVector<PitchData> CSVReader::removeDuplicatesKeepLatest(const QVector<PitchData> &data) {
    QVector<PitchData> result;
    QSet<QString>      processedNbrs;

    // 从后向前遍历，保留最新的数据
    for (int i = data.size() - 1; i >= 0; i--) {
        const PitchData &item = data[i];
        QString          nbr  = item.getNbr();

        if (!processedNbrs.contains(nbr)) {
            result.prepend(item);  // 插入到前面保持原有顺序
            processedNbrs.insert(nbr);
        }
    }

    qDebug() << "去重处理 - 原始数据:" << data.size() << "去重后:" << result.size();
    return result;
}

// 获取真正需要上传的数据（去重 + 过滤已录入）
QVector<PitchData> CSVReader::getActualUploadData(const QVector<PitchData> &data) {
    // 第一步：去重，保留最新数据
    QVector<PitchData> uniqueData = removeDuplicatesKeepLatest(data);

    // 第二步：过滤掉已录入的数据，只保留需要处理的
    QVector<PitchData> needsProcessing = filterNeedsProcessing(uniqueData);

    qDebug() << "获取真正需要上传的数据 - 原始:" << data.size() << "去重后:" << uniqueData.size() << "需要上传:" << needsProcessing.size();

    return needsProcessing;
}

// 私有辅助方法实现

// 解析录入状态字符串
UploadStatus CSVReader::parseUploadStatus(const QString &statusStr) {
    if (statusStr.trimmed().isEmpty()) {
        return UploadStatus::NotUploaded;
    }

    bool ok;
    int  statusInt = statusStr.toInt(&ok);

    if (!ok) {
        return UploadStatus::NotUploaded;
    }

    switch (statusInt) {
    case 1:
        return UploadStatus::Uploaded;
    case -1:
        return UploadStatus::Failed;
    default:
        return UploadStatus::NotUploaded;
    }
}

// 将录入状态转换为字符串
QString CSVReader::uploadStatusToString(UploadStatus status) {
    switch (status) {
    case UploadStatus::Uploaded:
        return "1";
    case UploadStatus::Failed:
        return "-1";
    case UploadStatus::NotUploaded:
    default:
        return "";
    }
}

// 读取文件所有行
QStringList CSVReader::readAllLines(const QString &fileName) {
    QStringList lines;
    QFile       file(fileName);

    if (!file.open(QIODevice::ReadOnly)) {
        qDebug() << "无法打开文件读取:" << fileName;
        return lines;
    }

    QTextStream in(&file);
    while (!in.atEnd()) {
        lines.append(in.readLine());
    }

    return lines;
}

// 写入文件所有行
bool CSVReader::writeAllLines(const QString &fileName, const QStringList &lines) {
    QFile file(fileName);

    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qDebug() << "无法打开文件写入:" << fileName;
        return false;
    }

    QTextStream out(&file);
    for (const QString &line : lines) {
        out << line << "\n";
    }

    return true;
}
