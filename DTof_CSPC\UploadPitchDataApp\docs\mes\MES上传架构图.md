# MES上传系统架构

> **单一来源**: 本文档仅提供架构图表。详细实现请参考 [[MES数据上传功能文档]]

## 系统架构概览

```mermaid
graph TB
    subgraph "用户界面层"
        UI[Widget UI]
        FileSelect[文件选择]
        Progress[进度显示]
    end
    
    subgraph "控制层"
        Widget[Widget 主控制器]
        Signal[信号槽机制]
    end
    
    subgraph "数据处理层"
        CSVReader[CSV读取器]
        DataValidator[数据验证器]
        MESBuilder[MES数据构建器]
    end
    
    subgraph "数据模型层"
        PitchData[PitchData 俯仰角数据]
        MESData[MESData MES数据结构]
        Config[配置数据]
    end
    
    subgraph "数据访问层"
        DataStore[DataStore 数据库操作]
        Database[(MES数据库)]
    end
    
    subgraph "输出层"
        CSVWriter[CSV结果输出]
        Reports[报告文件]
    end
    
    UI --> Widget
    FileSelect --> CSVReader
    CSVReader --> PitchData
    Widget --> DataValidator
    DataValidator --> MESBuilder
    MESBuilder --> MESData
    Widget --> DataStore
    DataStore --> Database
    Widget --> CSVWriter
    CSVWriter --> Reports
    Widget --> Progress
```

## 数据流程图

```mermaid
flowchart TD
    Start([开始]) --> SelectFile[选择CSV文件]
    SelectFile --> ReadCSV[读取CSV数据]
    ReadCSV --> ValidateData{数据验证}
    
    ValidateData -->|固件版本错误| FirmwareError[记录固件版本异常]
    ValidateData -->|MES信息错误| MESInfoError[记录MES信息异常]
    ValidateData -->|验证通过| BuildMESData[构建MES数据]
    
    BuildMESData --> ProcessLoop{处理每条数据}
    ProcessLoop --> UpdateAssociation{需要更新关联?}
    
    UpdateAssociation -->|是| UpdateTopNbr[更新标签关联]
    UpdateAssociation -->|否| GetTransaction[获取事务号]
    UpdateTopNbr --> GetTransaction
    
    GetTransaction --> UploadData[上传数据]
    UploadData --> UploadResult{上传结果}
    
    UploadResult -->|成功| RecordSuccess[记录成功数据]
    UploadResult -->|失败| RetryCheck{重试次数<5?}
    
    RetryCheck -->|是| GetTransaction
    RetryCheck -->|否| RecordFailure[记录失败数据]
    
    RecordSuccess --> NextData{还有数据?}
    RecordFailure --> NextData
    NextData -->|是| ProcessLoop
    NextData -->|否| GenerateReports[生成报告]
    
    FirmwareError --> GenerateReports
    MESInfoError --> GenerateReports
    GenerateReports --> End([结束])
```

## 类关系图

```mermaid
classDiagram
    class Widget {
        -ConfigLoader configLoader
        -XmlConfig config
        -DataStore dataStore
        -CSVReader csvReader
        -CSVWriter csvWriter
        +on_selectUploadFileBtn_clicked()
        +uploadMesDataSlot()
        -isHege(float, float, float) bool
    }
    
    class CSVReader {
        +readDataFromCsv(QString) QVector~PitchData~
    }
    
    class PitchData {
        +QString nbr
        +QString mcuID
        +QString firmwareVersion
        +int testResult
        +float pitchAngleValueMin
        +float pitchAngleValueMax
        +float pitchAngleValueStandardMin
        +float pitchAngleValueStandardMax
    }
    
    class MESData {
        +QString domain
        +uint32_t trnbr
        +QString nbr
        +QString op
        +QString userID
        +QString date
        +uint time
        +int testResult
        +QString rsnCode
        +QString station
        +QString topNbr
        +QString workOrder
        +QString mcuID
        +QString projrct1
        +QString stand1
        +float act1
        +int hege1
        +QString projrct2
        +QString stand2
        +float act2
        +int hege2
        +bool isNeedUpdateTopNbr
        +QString SNCode
    }
    
    class DataStore {
        +open(QSqlDatabase) SqlErrorType
        +close(QSqlDatabase) void
        +uploadMesData(QSqlDatabase, MESData) uint8_t
        +updataTopNbrForNbr(QSqlDatabase, QString, QString, QString) SqlErrorType
        +findXsubTrnbrMax(QSqlDatabase, QString, uint32_t) SqlErrorType
        -createInsertSql(MESData) QString
    }
    
    class CSVWriter {
        +writeMesInfoErrorData(QVector~PitchData~, QString, CSVFileType) void
        +writeMesData(QVector~MESData~, QString, CSVFileType) void
    }
    
    class ConfigLoader {
        +loadConfig() XmlConfig
    }
    
    class XmlConfig {
        +QString workOrder
        +QString userID
        +QString op
        +QString station
        +QString firmwareVersion
        +QString dataSourceName
        +QString hostName
        +QString port
        +QString dataBaseUserID
        +QString password
    }
    
    Widget --> CSVReader : uses
    Widget --> DataStore : uses
    Widget --> CSVWriter : uses
    Widget --> ConfigLoader : uses
    Widget --> XmlConfig : uses
    CSVReader --> PitchData : creates
    Widget --> MESData : creates
    DataStore --> MESData : processes
    CSVWriter --> MESData : outputs
    CSVWriter --> PitchData : outputs
```

## 数据库交互图

```mermaid
sequenceDiagram
    participant W as Widget
    participant DS as DataStore
    participant DB as MES Database
    
    W->>DS: open(odbcDB)
    DS->>DB: 建立连接
    DB-->>DS: 连接成功
    DS-->>W: SqlErrorType::NoError
    
    loop 每条MES数据
        alt 需要更新标签关联
            W->>DS: updataTopNbrForNbr()
            DS->>DB: UPDATE 标签关联
            DB-->>DS: 更新结果
            DS-->>W: SqlErrorType
        end
        
        loop 重试最多5次
            W->>DS: findXsubTrnbrMax()
            DS->>DB: SELECT MAX(trnbr)
            DB-->>DS: 最大事务号
            DS-->>W: 事务号+1
            
            W->>DS: uploadMesData()
            DS->>DB: INSERT INTO pub.xsub6_det
            DB-->>DS: 插入结果
            DS-->>W: SqlErrorType
            
            alt 上传成功
                break 退出重试循环
            end
        end
    end
    
    W->>DS: close(odbcDB)
    DS->>DB: 关闭连接
```

---
**相关文档**: [[MES数据上传功能文档]] | [[俯仰角测试用户手册]]