# MES数据上传功能测试

## 测试概述

本测试程序用于验证MES数据上传功能的各个组件，确保代码优化后的功能正常工作。

## 测试内容

### 1. CSV文件读取测试
- 读取原始CSV文件
- 验证数据解析正确性
- 检查录入状态字段

### 2. 工作拷贝功能测试
- 检测文件类型（原始文件 vs 工作拷贝）
- 创建工作拷贝文件
- 验证状态列添加

### 3. 数据处理功能测试
- 重复数据去重（保留最新）
- 过滤需要处理的数据
- 获取真正需要上传的数据

### 4. 状态管理测试
- 按电机标签更新状态
- 验证状态持久化
- 检查状态更新逻辑

## 编译和运行

### 使用CMake编译
```bash
cd tests
mkdir build
cd build
cmake ..
make
```

### 直接运行
```bash
./bin/MESUploadTest
```

## 测试数据

测试使用的数据文件：
- `../build/bin/俯仰角视觉检测_195_20250623143833252.csv`

## 预期输出

测试程序会输出详细的测试结果，包括：
- 数据读取统计
- 去重处理结果
- 状态更新验证
- 各项功能的执行状态

## 测试验证点

1. **文件处理**：
   - ✅ 原始文件正确识别
   - ✅ 工作拷贝成功创建
   - ✅ 状态列正确添加

2. **数据处理**：
   - ✅ 重复数据正确去重
   - ✅ 最新数据优先保留
   - ✅ 需要处理数据正确过滤

3. **状态管理**：
   - ✅ 状态更新功能正常
   - ✅ 最新数据状态更新
   - ✅ 状态持久化成功

## 故障排除

如果测试失败，请检查：
1. 测试数据文件是否存在
2. Qt环境是否正确配置
3. 编译依赖是否完整
4. 文件权限是否正确
