#ifndef PITCHDATA_H
#define PITCHDATA_H

#include <QString>


class PitchData
{
public:
    PitchData() = default;
    explicit PitchData(const QString& nbr,const QString& mcuID,const QString& firmwareVersion, int testResult, float pitchAngleValueMin, float pitchAngleValueMax, float pitchAngleValueStandardMin, float pitchAngleValueStandardMax);

    const QString& getNbr() const;
    const QString& getMcuID() const;
    const QString& getFirmwareVersion() const;
    int getTestResult() const;
    float getPitchAngleValueMin() const;
    float getPitchAngleValueMax() const;
    float getPitchAngleValueStandardMin() const;
    float getPitchAngleValueStandardMax() const;

private:
    QString nbr;
    QString mcuID;
    QString firmwareVersion;
    int testResult;
    float pitchAngleValueMin;
    float pitchAngleValueMax;
    float pitchAngleValueStandardMin;
    float pitchAngleValueStandardMax;

};

#endif // PITCHDATA_H
