#ifndef PITCHDATA_H
#define PITCHDATA_H

#include <QString>


// 录入状态枚举
enum class UploadStatus {
    NotUploaded = 0,  // 未录入（空值）
    Uploaded    = 1,  // 已成功录入
    Failed      = -1  // 录入异常
};

class PitchData {
  public:
    PitchData() = default;
    explicit PitchData(const QString &nbr,
                       const QString &mcuID,
                       const QString &firmwareVersion,
                       int            testResult,
                       float          pitchAngleValueMin,
                       float          pitchAngleValueMax,
                       float          pitchAngleValueStandardMin,
                       float          pitchAngleValueStandardMax,
                       UploadStatus   uploadStatus = UploadStatus::NotUploaded,
                       int            rowIndex     = -1);

    // 原有的getter方法
    const QString &getNbr() const;
    const QString &getMcuID() const;
    const QString &getFirmwareVersion() const;
    int            getTestResult() const;
    float          getPitchAngleValueMin() const;
    float          getPitchAngleValueMax() const;
    float          getPitchAngleValueStandardMin() const;
    float          getPitchAngleValueStandardMax() const;

    // 新增的getter/setter方法
    UploadStatus getUploadStatus() const;
    void         setUploadStatus(UploadStatus status);
    int          getRowIndex() const;
    void         setRowIndex(int index);

    // 工具方法
    bool isUploaded() const;
    bool isFailed() const;
    bool needsProcessing() const;

  private:
    QString nbr;
    QString mcuID;
    QString firmwareVersion;
    int     testResult;
    float   pitchAngleValueMin;
    float   pitchAngleValueMax;
    float   pitchAngleValueStandardMin;
    float   pitchAngleValueStandardMax;

    // 新增字段
    UploadStatus uploadStatus;  // 录入状态
    int          rowIndex;      // 在CSV文件中的行索引（用于状态更新）
};

#endif  // PITCHDATA_H
