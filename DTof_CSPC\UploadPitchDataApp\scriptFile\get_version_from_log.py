'''
Author: 
Date: 
LastEditors: 
LastEditTime: 
Description: 从CHANGELOG.md中获取版本号
FilePath: ..\\scriptFile\\get_version_from_log.py
'''
#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import re
import sys

def get_latest_version(changelog_path):
    if not os.path.exists(changelog_path):
        return "0.0.0"
    with open(changelog_path, 'r', encoding='utf-8') as f:
        for line in f:
            match = re.match(r'^## \[v(\d+\.\d+\.\d+)\]', line)
            if match:
                return match.group(1)
    return "0.0.0"

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python get_version_from_log.py <directory_path>")
        sys.exit(1)
    
    directory_path = sys.argv[1]
    changelog_path = os.path.join(directory_path, 'CHANGELOG.md')
    latest_version = get_latest_version(changelog_path)
    print(latest_version)

