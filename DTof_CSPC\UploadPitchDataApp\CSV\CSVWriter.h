#ifndef CSVWRITER_H
#define CSVWRITER_H

#include <QObject>
#include "Pojo/MESData.h"
#include "Pojo/PitchData.h"

enum class CSVFileType {
    FirmewareVersionErr = 0,
    MESInfoErr,
    MESUploadFailed,
    MesUploadSuccess
};

class CSVWriter : public QObject
{
    Q_OBJECT
public:
    explicit CSVWriter(QObject *parent = nullptr);

    void writeMesInfoErrorData(const QVector<PitchData>& mesInfoErrorData, const QString& time, const CSVFileType flag);

    void writeMesData(const QVector<MESData>& mesData, const QString& time, const CSVFileType flag);
signals:

};

#endif // CSVWRITER_H
