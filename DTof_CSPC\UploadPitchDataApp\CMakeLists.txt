cmake_minimum_required(VERSION 3.5)

project(UploadPitchDataApp VERSION 0.1 LANGUAGES CXX)

set(CMAKE_INCLUDE_CURRENT_DIR ON)

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(QT NAMES Qt6 Qt5 COMPONENTS Core Widgets Sql SerialPort REQUIRED)
find_package(Qt${QT_VERSION_MAJOR} COMPONENTS Core Widgets Sql SerialPort REQUIRED)

# 设置源文件编码为UTF-8
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -finput-charset=UTF-8")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -finput-charset=UTF-8")

# Windows平台额外设置（Visual Studio）
if(MSVC)
    # 设置编码
    add_compile_options("$<$<C_COMPILER_ID:MSVC>:/utf-8>")
    add_compile_options("$<$<CXX_COMPILER_ID:MSVC>:/utf-8>")

    # 解决乱码问题
    add_compile_options(/utf-8) # Visual Studio 2015及以上支持
endif()

ADD_DEFINITIONS(-DQT_MESSAGELOGCONTEXT) #QDebug在release下依然可以输出函数名和行号

# 要放在find_package后 否则识别不了qt5_add_resources命令
set(QRC_SOURCE_FILE res.qrc)    # CmakeLists.txt和res.qrc文件同级
qt5_add_resources(${QRC_SOURCE_FILE})

# set(TARGET_NAME UploadPitchDataAppV1.0)

set(PROJECT_SOURCES
        main.cpp
        widget.cpp
        widget.h
        widget.ui

        DataStore/DataStore.h
        DataStore/DataStore.cpp

        Config/ConfigLoader.h
        Config/ConfigLoader.cpp
        Config/XmlConfig.h
        Config/XmlConfig.cpp

        Pojo/PitchData.h
        Pojo/PitchData.cpp
        Pojo/MESData.h
        Pojo/MESData.cpp
#        Pojo/OutputPitchData.h
#        Pojo/OutputPitchData.cpp

        CSV/CSVReader.h
        CSV/CSVReader.cpp
        CSV/CSVWriter.h
        CSV/CSVWriter.cpp

        Comm.h
        Comm.cpp
        Comm.ui

        MainForm.h
        MainForm.cpp
        MainForm.ui

        version.rc

        ${QRC_SOURCE_FILE}
)

set(PROGRAM_PREFIX  "CSPC")

########################################### 编译 ###########################
# 3.1 版本号处理
set(Python3_EXECUTABLE "D:/Programs/Python313/python.exe")
find_package(Python3 REQUIRED COMPONENTS Interpreter)

#   读取 CHANGELOG.md 中的最新版本号
execute_process(
    COMMAND ${Python3_EXECUTABLE} ${CMAKE_SOURCE_DIR}/scriptFile/get_version_from_log.py ${CMAKE_CURRENT_SOURCE_DIR}
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    OUTPUT_VARIABLE SW_VERSION
    OUTPUT_STRIP_TRAILING_WHITESPACE
)
#   读取生成软件类型：测试版(build)，发布版(git commit)
execute_process(
    COMMAND ${Python3_EXECUTABLE} ${CMAKE_SOURCE_DIR}/scriptFile/get_sw_type.py ${CMAKE_SOURCE_DIR}
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    OUTPUT_VARIABLE SW_TYPE
    OUTPUT_STRIP_TRAILING_WHITESPACE
)

message(STATUS "${PROJECT_NAME} project version: ${SW_VERSION}")
#   将项目+版本号存储在变量中
set(PROJECT_VERSION "${PROGRAM_PREFIX}_${PROJECT_NAME}_V${SW_VERSION}")

#   设置窗口标题
add_definitions(-DMAIN_APP_WINDOW_TITLE="${PROJECT_VERSION}")

# 3.2 设置目标名称
string(TIMESTAMP CURRENT_DATE "%Y%m%d") #   获取当前日期
# release-发布版 
# test-测试版, release build
# debug-调试版，debug build
string(STRIP "${SW_TYPE}" SW_TYPE)
if(SW_TYPE STREQUAL "release") 
set(TARGET_NAME "${PROGRAM_PREFIX}_${PROJECT_NAME}_v${SW_VERSION}_${CURRENT_DATE}_${BUILD_MODE}") #   使用新版本号命名生成的可执行文件
else()
# set(TARGET_NAME "${PROGRAM_PREFIX}_${PROJECT_NAME}_v${SW_VERSION}_${CURRENT_DATE}_${SW_TYPE}") #   使用新版本号命名生成的可执行文件
set(TARGET_NAME "${PROGRAM_PREFIX}_${PROJECT_NAME}") #   使用新版本号命名生成的可执行文件
endif()

set(BASE_OUTPUT_DIR "${CMAKE_BINARY_DIR}")
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${BASE_OUTPUT_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${BASE_OUTPUT_DIR}/bin)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${BASE_OUTPUT_DIR}/lib)

if(${QT_VERSION_MAJOR} GREATER_EQUAL 6)
    qt_add_executable(${TARGET_NAME}
        MANUAL_FINALIZATION
        ${PROJECT_SOURCES}

    )
# Define target properties for Android with Qt 6 as:
#    set_property(TARGET UploadPitchDataApp APPEND PROPERTY QT_ANDROID_PACKAGE_SOURCE_DIR
#                 ${CMAKE_CURRENT_SOURCE_DIR}/android)
# For more information, see https://doc.qt.io/qt-6/qt-add-executable.html#target-creation
else()
    if(ANDROID)
        add_library(${TARGET_NAME} SHARED
            ${PROJECT_SOURCES}

        )
# Define properties for Android with Qt 5 after find_package() calls as:
#    set(ANDROID_PACKAGE_SOURCE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/android")
    else()
        add_executable(${TARGET_NAME}
            ${PROJECT_SOURCES}
        )
    endif()
endif()

target_link_libraries(${TARGET_NAME} PRIVATE
    Qt${QT_VERSION_MAJOR}::Widgets
    Qt${QT_VERSION_MAJOR}::Sql
    Qt${QT_VERSION_MAJOR}::Core
    Qt${QT_VERSION_MAJOR}::SerialPort)

set_target_properties(${TARGET_NAME} PROPERTIES
    MACOSX_BUNDLE_GUI_IDENTIFIER my.example.com
    MACOSX_BUNDLE_BUNDLE_VERSION ${PROJECT_VERSION}
    MACOSX_BUNDLE_SHORT_VERSION_STRING ${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}
)

if(QT_VERSION_MAJOR EQUAL 6)
    qt_finalize_executable(${TARGET_NAME})
endif()


# 视觉软件与本软件串口通讯协议
# 视觉发送<MCUID> 本软件接收到并向雷达发送读取MCUID的指令 解析并回传
# 视觉发送<SoftwareVersion> 本软件接收到并向雷达发送读取固件版本的指令 解析并回传
# 视觉发送的指令不能识别或者没有接收到雷达数据 本软件回复<0>


