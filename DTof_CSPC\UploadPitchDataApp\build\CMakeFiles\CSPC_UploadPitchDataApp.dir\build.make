# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.21

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\Programs\CMake\bin\cmake.exe

# The command to remove a file.
RM = D:\Programs\CMake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build

# Include any dependencies generated for this target.
include CMakeFiles/CSPC_UploadPitchDataApp.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/CSPC_UploadPitchDataApp.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/CSPC_UploadPitchDataApp.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/CSPC_UploadPitchDataApp.dir/flags.make

CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp: ../res.qrc
CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp: CMakeFiles/CSPC_UploadPitchDataApp_autogen.dir/AutoRcc_res_EWIEGA46WW_Info.json
CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp: ../resource/cspc.ico
CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp: ../resource/cspc.jpg
CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp: D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/bin/rcc.exe
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic RCC for res.qrc"
	D:\Programs\CMake\bin\cmake.exe -E cmake_autorcc F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build/CMakeFiles/CSPC_UploadPitchDataApp_autogen.dir/AutoRcc_res_EWIEGA46WW_Info.json MinSizeRel

CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/flags.make
CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp
CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.obj: CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp
CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.obj"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.obj -MF CMakeFiles\CSPC_UploadPitchDataApp.dir\CSPC_UploadPitchDataApp_autogen\mocs_compilation.cpp.obj.d -o CMakeFiles\CSPC_UploadPitchDataApp.dir\CSPC_UploadPitchDataApp_autogen\mocs_compilation.cpp.obj -c F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CSPC_UploadPitchDataApp_autogen\mocs_compilation.cpp

CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.i"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CSPC_UploadPitchDataApp_autogen\mocs_compilation.cpp > CMakeFiles\CSPC_UploadPitchDataApp.dir\CSPC_UploadPitchDataApp_autogen\mocs_compilation.cpp.i

CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.s"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CSPC_UploadPitchDataApp_autogen\mocs_compilation.cpp -o CMakeFiles\CSPC_UploadPitchDataApp.dir\CSPC_UploadPitchDataApp_autogen\mocs_compilation.cpp.s

CMakeFiles/CSPC_UploadPitchDataApp.dir/main.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/flags.make
CMakeFiles/CSPC_UploadPitchDataApp.dir/main.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp
CMakeFiles/CSPC_UploadPitchDataApp.dir/main.cpp.obj: ../main.cpp
CMakeFiles/CSPC_UploadPitchDataApp.dir/main.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/CSPC_UploadPitchDataApp.dir/main.cpp.obj"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CSPC_UploadPitchDataApp.dir/main.cpp.obj -MF CMakeFiles\CSPC_UploadPitchDataApp.dir\main.cpp.obj.d -o CMakeFiles\CSPC_UploadPitchDataApp.dir\main.cpp.obj -c F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\main.cpp

CMakeFiles/CSPC_UploadPitchDataApp.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/CSPC_UploadPitchDataApp.dir/main.cpp.i"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\main.cpp > CMakeFiles\CSPC_UploadPitchDataApp.dir\main.cpp.i

CMakeFiles/CSPC_UploadPitchDataApp.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/CSPC_UploadPitchDataApp.dir/main.cpp.s"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\main.cpp -o CMakeFiles\CSPC_UploadPitchDataApp.dir\main.cpp.s

CMakeFiles/CSPC_UploadPitchDataApp.dir/widget.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/flags.make
CMakeFiles/CSPC_UploadPitchDataApp.dir/widget.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp
CMakeFiles/CSPC_UploadPitchDataApp.dir/widget.cpp.obj: ../widget.cpp
CMakeFiles/CSPC_UploadPitchDataApp.dir/widget.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/CSPC_UploadPitchDataApp.dir/widget.cpp.obj"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CSPC_UploadPitchDataApp.dir/widget.cpp.obj -MF CMakeFiles\CSPC_UploadPitchDataApp.dir\widget.cpp.obj.d -o CMakeFiles\CSPC_UploadPitchDataApp.dir\widget.cpp.obj -c F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\widget.cpp

CMakeFiles/CSPC_UploadPitchDataApp.dir/widget.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/CSPC_UploadPitchDataApp.dir/widget.cpp.i"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\widget.cpp > CMakeFiles\CSPC_UploadPitchDataApp.dir\widget.cpp.i

CMakeFiles/CSPC_UploadPitchDataApp.dir/widget.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/CSPC_UploadPitchDataApp.dir/widget.cpp.s"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\widget.cpp -o CMakeFiles\CSPC_UploadPitchDataApp.dir\widget.cpp.s

CMakeFiles/CSPC_UploadPitchDataApp.dir/DataStore/DataStore.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/flags.make
CMakeFiles/CSPC_UploadPitchDataApp.dir/DataStore/DataStore.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp
CMakeFiles/CSPC_UploadPitchDataApp.dir/DataStore/DataStore.cpp.obj: ../DataStore/DataStore.cpp
CMakeFiles/CSPC_UploadPitchDataApp.dir/DataStore/DataStore.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/CSPC_UploadPitchDataApp.dir/DataStore/DataStore.cpp.obj"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CSPC_UploadPitchDataApp.dir/DataStore/DataStore.cpp.obj -MF CMakeFiles\CSPC_UploadPitchDataApp.dir\DataStore\DataStore.cpp.obj.d -o CMakeFiles\CSPC_UploadPitchDataApp.dir\DataStore\DataStore.cpp.obj -c F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\DataStore\DataStore.cpp

CMakeFiles/CSPC_UploadPitchDataApp.dir/DataStore/DataStore.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/CSPC_UploadPitchDataApp.dir/DataStore/DataStore.cpp.i"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\DataStore\DataStore.cpp > CMakeFiles\CSPC_UploadPitchDataApp.dir\DataStore\DataStore.cpp.i

CMakeFiles/CSPC_UploadPitchDataApp.dir/DataStore/DataStore.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/CSPC_UploadPitchDataApp.dir/DataStore/DataStore.cpp.s"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\DataStore\DataStore.cpp -o CMakeFiles\CSPC_UploadPitchDataApp.dir\DataStore\DataStore.cpp.s

CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/ConfigLoader.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/flags.make
CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/ConfigLoader.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp
CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/ConfigLoader.cpp.obj: ../Config/ConfigLoader.cpp
CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/ConfigLoader.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/ConfigLoader.cpp.obj"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/ConfigLoader.cpp.obj -MF CMakeFiles\CSPC_UploadPitchDataApp.dir\Config\ConfigLoader.cpp.obj.d -o CMakeFiles\CSPC_UploadPitchDataApp.dir\Config\ConfigLoader.cpp.obj -c F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\Config\ConfigLoader.cpp

CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/ConfigLoader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/ConfigLoader.cpp.i"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\Config\ConfigLoader.cpp > CMakeFiles\CSPC_UploadPitchDataApp.dir\Config\ConfigLoader.cpp.i

CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/ConfigLoader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/ConfigLoader.cpp.s"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\Config\ConfigLoader.cpp -o CMakeFiles\CSPC_UploadPitchDataApp.dir\Config\ConfigLoader.cpp.s

CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/XmlConfig.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/flags.make
CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/XmlConfig.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp
CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/XmlConfig.cpp.obj: ../Config/XmlConfig.cpp
CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/XmlConfig.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/XmlConfig.cpp.obj"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/XmlConfig.cpp.obj -MF CMakeFiles\CSPC_UploadPitchDataApp.dir\Config\XmlConfig.cpp.obj.d -o CMakeFiles\CSPC_UploadPitchDataApp.dir\Config\XmlConfig.cpp.obj -c F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\Config\XmlConfig.cpp

CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/XmlConfig.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/XmlConfig.cpp.i"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\Config\XmlConfig.cpp > CMakeFiles\CSPC_UploadPitchDataApp.dir\Config\XmlConfig.cpp.i

CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/XmlConfig.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/XmlConfig.cpp.s"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\Config\XmlConfig.cpp -o CMakeFiles\CSPC_UploadPitchDataApp.dir\Config\XmlConfig.cpp.s

CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/PitchData.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/flags.make
CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/PitchData.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp
CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/PitchData.cpp.obj: ../Pojo/PitchData.cpp
CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/PitchData.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/PitchData.cpp.obj"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/PitchData.cpp.obj -MF CMakeFiles\CSPC_UploadPitchDataApp.dir\Pojo\PitchData.cpp.obj.d -o CMakeFiles\CSPC_UploadPitchDataApp.dir\Pojo\PitchData.cpp.obj -c F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\Pojo\PitchData.cpp

CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/PitchData.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/PitchData.cpp.i"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\Pojo\PitchData.cpp > CMakeFiles\CSPC_UploadPitchDataApp.dir\Pojo\PitchData.cpp.i

CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/PitchData.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/PitchData.cpp.s"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\Pojo\PitchData.cpp -o CMakeFiles\CSPC_UploadPitchDataApp.dir\Pojo\PitchData.cpp.s

CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/MESData.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/flags.make
CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/MESData.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp
CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/MESData.cpp.obj: ../Pojo/MESData.cpp
CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/MESData.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/MESData.cpp.obj"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/MESData.cpp.obj -MF CMakeFiles\CSPC_UploadPitchDataApp.dir\Pojo\MESData.cpp.obj.d -o CMakeFiles\CSPC_UploadPitchDataApp.dir\Pojo\MESData.cpp.obj -c F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\Pojo\MESData.cpp

CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/MESData.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/MESData.cpp.i"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\Pojo\MESData.cpp > CMakeFiles\CSPC_UploadPitchDataApp.dir\Pojo\MESData.cpp.i

CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/MESData.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/MESData.cpp.s"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\Pojo\MESData.cpp -o CMakeFiles\CSPC_UploadPitchDataApp.dir\Pojo\MESData.cpp.s

CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVReader.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/flags.make
CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVReader.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp
CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVReader.cpp.obj: ../CSV/CSVReader.cpp
CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVReader.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVReader.cpp.obj"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVReader.cpp.obj -MF CMakeFiles\CSPC_UploadPitchDataApp.dir\CSV\CSVReader.cpp.obj.d -o CMakeFiles\CSPC_UploadPitchDataApp.dir\CSV\CSVReader.cpp.obj -c F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\CSV\CSVReader.cpp

CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVReader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVReader.cpp.i"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\CSV\CSVReader.cpp > CMakeFiles\CSPC_UploadPitchDataApp.dir\CSV\CSVReader.cpp.i

CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVReader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVReader.cpp.s"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\CSV\CSVReader.cpp -o CMakeFiles\CSPC_UploadPitchDataApp.dir\CSV\CSVReader.cpp.s

CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVWriter.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/flags.make
CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVWriter.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp
CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVWriter.cpp.obj: ../CSV/CSVWriter.cpp
CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVWriter.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVWriter.cpp.obj"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVWriter.cpp.obj -MF CMakeFiles\CSPC_UploadPitchDataApp.dir\CSV\CSVWriter.cpp.obj.d -o CMakeFiles\CSPC_UploadPitchDataApp.dir\CSV\CSVWriter.cpp.obj -c F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\CSV\CSVWriter.cpp

CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVWriter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVWriter.cpp.i"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\CSV\CSVWriter.cpp > CMakeFiles\CSPC_UploadPitchDataApp.dir\CSV\CSVWriter.cpp.i

CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVWriter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVWriter.cpp.s"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\CSV\CSVWriter.cpp -o CMakeFiles\CSPC_UploadPitchDataApp.dir\CSV\CSVWriter.cpp.s

CMakeFiles/CSPC_UploadPitchDataApp.dir/Comm.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/flags.make
CMakeFiles/CSPC_UploadPitchDataApp.dir/Comm.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp
CMakeFiles/CSPC_UploadPitchDataApp.dir/Comm.cpp.obj: ../Comm.cpp
CMakeFiles/CSPC_UploadPitchDataApp.dir/Comm.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object CMakeFiles/CSPC_UploadPitchDataApp.dir/Comm.cpp.obj"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CSPC_UploadPitchDataApp.dir/Comm.cpp.obj -MF CMakeFiles\CSPC_UploadPitchDataApp.dir\Comm.cpp.obj.d -o CMakeFiles\CSPC_UploadPitchDataApp.dir\Comm.cpp.obj -c F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\Comm.cpp

CMakeFiles/CSPC_UploadPitchDataApp.dir/Comm.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/CSPC_UploadPitchDataApp.dir/Comm.cpp.i"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\Comm.cpp > CMakeFiles\CSPC_UploadPitchDataApp.dir\Comm.cpp.i

CMakeFiles/CSPC_UploadPitchDataApp.dir/Comm.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/CSPC_UploadPitchDataApp.dir/Comm.cpp.s"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\Comm.cpp -o CMakeFiles\CSPC_UploadPitchDataApp.dir\Comm.cpp.s

CMakeFiles/CSPC_UploadPitchDataApp.dir/MainForm.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/flags.make
CMakeFiles/CSPC_UploadPitchDataApp.dir/MainForm.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp
CMakeFiles/CSPC_UploadPitchDataApp.dir/MainForm.cpp.obj: ../MainForm.cpp
CMakeFiles/CSPC_UploadPitchDataApp.dir/MainForm.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object CMakeFiles/CSPC_UploadPitchDataApp.dir/MainForm.cpp.obj"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CSPC_UploadPitchDataApp.dir/MainForm.cpp.obj -MF CMakeFiles\CSPC_UploadPitchDataApp.dir\MainForm.cpp.obj.d -o CMakeFiles\CSPC_UploadPitchDataApp.dir\MainForm.cpp.obj -c F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\MainForm.cpp

CMakeFiles/CSPC_UploadPitchDataApp.dir/MainForm.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/CSPC_UploadPitchDataApp.dir/MainForm.cpp.i"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\MainForm.cpp > CMakeFiles\CSPC_UploadPitchDataApp.dir\MainForm.cpp.i

CMakeFiles/CSPC_UploadPitchDataApp.dir/MainForm.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/CSPC_UploadPitchDataApp.dir/MainForm.cpp.s"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\MainForm.cpp -o CMakeFiles\CSPC_UploadPitchDataApp.dir\MainForm.cpp.s

CMakeFiles/CSPC_UploadPitchDataApp.dir/version.rc.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/flags.make
CMakeFiles/CSPC_UploadPitchDataApp.dir/version.rc.obj: ../version.rc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building RC object CMakeFiles/CSPC_UploadPitchDataApp.dir/version.rc.obj"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\windres.exe -O coff $(RC_DEFINES) $(RC_INCLUDES) $(RC_FLAGS) F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\version.rc CMakeFiles\CSPC_UploadPitchDataApp.dir\version.rc.obj

CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/flags.make
CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp
CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.obj: CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp
CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.obj: CMakeFiles/CSPC_UploadPitchDataApp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.obj"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.obj -MF CMakeFiles\CSPC_UploadPitchDataApp.dir\CSPC_UploadPitchDataApp_autogen\EWIEGA46WW\qrc_res.cpp.obj.d -o CMakeFiles\CSPC_UploadPitchDataApp.dir\CSPC_UploadPitchDataApp_autogen\EWIEGA46WW\qrc_res.cpp.obj -c F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CSPC_UploadPitchDataApp_autogen\EWIEGA46WW\qrc_res.cpp

CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.i"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CSPC_UploadPitchDataApp_autogen\EWIEGA46WW\qrc_res.cpp > CMakeFiles\CSPC_UploadPitchDataApp.dir\CSPC_UploadPitchDataApp_autogen\EWIEGA46WW\qrc_res.cpp.i

CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.s"
	D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CSPC_UploadPitchDataApp_autogen\EWIEGA46WW\qrc_res.cpp -o CMakeFiles\CSPC_UploadPitchDataApp.dir\CSPC_UploadPitchDataApp_autogen\EWIEGA46WW\qrc_res.cpp.s

# Object files for target CSPC_UploadPitchDataApp
CSPC_UploadPitchDataApp_OBJECTS = \
"CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.obj" \
"CMakeFiles/CSPC_UploadPitchDataApp.dir/main.cpp.obj" \
"CMakeFiles/CSPC_UploadPitchDataApp.dir/widget.cpp.obj" \
"CMakeFiles/CSPC_UploadPitchDataApp.dir/DataStore/DataStore.cpp.obj" \
"CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/ConfigLoader.cpp.obj" \
"CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/XmlConfig.cpp.obj" \
"CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/PitchData.cpp.obj" \
"CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/MESData.cpp.obj" \
"CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVReader.cpp.obj" \
"CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVWriter.cpp.obj" \
"CMakeFiles/CSPC_UploadPitchDataApp.dir/Comm.cpp.obj" \
"CMakeFiles/CSPC_UploadPitchDataApp.dir/MainForm.cpp.obj" \
"CMakeFiles/CSPC_UploadPitchDataApp.dir/version.rc.obj" \
"CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.obj"

# External object files for target CSPC_UploadPitchDataApp
CSPC_UploadPitchDataApp_EXTERNAL_OBJECTS =

bin/CSPC_UploadPitchDataApp.exe: CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.obj
bin/CSPC_UploadPitchDataApp.exe: CMakeFiles/CSPC_UploadPitchDataApp.dir/main.cpp.obj
bin/CSPC_UploadPitchDataApp.exe: CMakeFiles/CSPC_UploadPitchDataApp.dir/widget.cpp.obj
bin/CSPC_UploadPitchDataApp.exe: CMakeFiles/CSPC_UploadPitchDataApp.dir/DataStore/DataStore.cpp.obj
bin/CSPC_UploadPitchDataApp.exe: CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/ConfigLoader.cpp.obj
bin/CSPC_UploadPitchDataApp.exe: CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/XmlConfig.cpp.obj
bin/CSPC_UploadPitchDataApp.exe: CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/PitchData.cpp.obj
bin/CSPC_UploadPitchDataApp.exe: CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/MESData.cpp.obj
bin/CSPC_UploadPitchDataApp.exe: CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVReader.cpp.obj
bin/CSPC_UploadPitchDataApp.exe: CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVWriter.cpp.obj
bin/CSPC_UploadPitchDataApp.exe: CMakeFiles/CSPC_UploadPitchDataApp.dir/Comm.cpp.obj
bin/CSPC_UploadPitchDataApp.exe: CMakeFiles/CSPC_UploadPitchDataApp.dir/MainForm.cpp.obj
bin/CSPC_UploadPitchDataApp.exe: CMakeFiles/CSPC_UploadPitchDataApp.dir/version.rc.obj
bin/CSPC_UploadPitchDataApp.exe: CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.obj
bin/CSPC_UploadPitchDataApp.exe: CMakeFiles/CSPC_UploadPitchDataApp.dir/build.make
bin/CSPC_UploadPitchDataApp.exe: D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/libQt5Widgets.a
bin/CSPC_UploadPitchDataApp.exe: D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/libQt5Sql.a
bin/CSPC_UploadPitchDataApp.exe: D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/libQt5SerialPort.a
bin/CSPC_UploadPitchDataApp.exe: D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/libQt5Gui.a
bin/CSPC_UploadPitchDataApp.exe: D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/lib/libQt5Core.a
bin/CSPC_UploadPitchDataApp.exe: CMakeFiles/CSPC_UploadPitchDataApp.dir/linklibs.rsp
bin/CSPC_UploadPitchDataApp.exe: CMakeFiles/CSPC_UploadPitchDataApp.dir/objects1.rsp
bin/CSPC_UploadPitchDataApp.exe: CMakeFiles/CSPC_UploadPitchDataApp.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Linking CXX executable bin\CSPC_UploadPitchDataApp.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\CSPC_UploadPitchDataApp.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/CSPC_UploadPitchDataApp.dir/build: bin/CSPC_UploadPitchDataApp.exe
.PHONY : CMakeFiles/CSPC_UploadPitchDataApp.dir/build

CMakeFiles/CSPC_UploadPitchDataApp.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\CSPC_UploadPitchDataApp.dir\cmake_clean.cmake
.PHONY : CMakeFiles/CSPC_UploadPitchDataApp.dir/clean

CMakeFiles/CSPC_UploadPitchDataApp.dir/depend: CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles\CSPC_UploadPitchDataApp.dir\DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/CSPC_UploadPitchDataApp.dir/depend

