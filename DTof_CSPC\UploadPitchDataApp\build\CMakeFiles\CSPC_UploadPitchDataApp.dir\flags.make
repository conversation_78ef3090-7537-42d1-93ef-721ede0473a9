# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.21

# compile CXX with D:/Programs/Qt/Qt5.14.2/Tools/mingw730_64/bin/x86_64-w64-mingw32-g++.exe
# compile RC with D:/Programs/Qt/Qt5.14.2/Tools/mingw730_64/bin/windres.exe
CXX_DEFINES = -DMAIN_APP_WINDOW_TITLE=\"CSPC_UploadPitchDataApp_V0.0.0\" -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MESSAGELOGCONTEXT -DQT_NO_DEBUG -DQT_SERIALPORT_LIB -DQT_SQL_LIB -DQT_WIDGETS_LIB

CXX_INCLUDES = @CMakeFiles/CSPC_UploadPitchDataApp.dir/includes_CXX.rsp

CXX_FLAGS =  -finput-charset=UTF-8 -Os -DNDEBUG -std=gnu++11

RC_DEFINES = -DMAIN_APP_WINDOW_TITLE=\"CSPC_UploadPitchDataApp_V0.0.0\" -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MESSAGELOGCONTEXT -DQT_NO_DEBUG -DQT_SERIALPORT_LIB -DQT_SQL_LIB -DQT_WIDGETS_LIB

RC_INCLUDES = -I F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build -I F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp -I F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CSPC_UploadPitchDataApp_autogen\include -I D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include -I D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtWidgets -I D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtGui -I D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtANGLE -I D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtCore -I D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\.\mkspecs\win32-g++ -I D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSql -I D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\include\QtSerialPort

RC_FLAGS = 

