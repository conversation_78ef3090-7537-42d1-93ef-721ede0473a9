#ifndef MESDATA_H
#define MESDATA_H

#include <QString>
class MESData
{
public:
    MESData();

    QString domain = "000";
    uint32_t trnbr = 0;
    QString nbr = "00000000";
    QString op = "000";
    QString userID = "000081";
    QString date;
    uint time;
    int testResult;
    QString rsnCode;
    QString station;
    QString topNbr;
    QString workOrder;
    QString mcuID;

    QString projrct1 = "MIN";
    QString stand1;
    float act1;
    int hege1;

    QString projrct2 = "MAX";
    QString stand2;
    float act2;
    int hege2;

    bool isNeedUpdateTopNbr = true;
    QString SNCode = "";

};

#endif // MESDATA_H
