#include "MainForm.h"
#include "ui_MainForm.h"

#include <QDebug>

Main::Main(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::Main)
{
    ui->setupUi(this);
    setWindowTitle("主界面V1.0");
}

Main::~Main()
{
    delete ui;

    if(nullptr != mesUi) {
        delete mesUi;
        mesUi = nullptr;
        qDebug() << QString::fromUtf8("delete mesUi");
    }

    if(nullptr != uartUi) {
        delete uartUi;
        uartUi = nullptr;
        qDebug() << QString::fromUtf8("delete uartUi");
    }

}

void Main::on_uartUiBtn_clicked()
{
    if(nullptr == uartUi) {
        uartUi = new Comm();
        qDebug() << QString::fromUtf8("create uartUi");
    }
    qDebug() << QString::fromUtf8("show uartUi");
    uartUi->show();
}


void Main::on_mesUiBtn_clicked()
{
    if(nullptr == mesUi) {
        mesUi = new Widget();
        qDebug() << QString::fromUtf8("create mesUi");
    }
    qDebug() << QString::fromUtf8("show mesUi");
    mesUi->show();
}

