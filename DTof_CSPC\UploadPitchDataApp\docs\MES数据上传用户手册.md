# 俯仰角测试系统用户手册

## 1. 系统概述

俯仰角测试系统是一套完整的激光雷达俯仰角视觉检测解决方案，主要功能包括：

- **俯仰角视觉检测**：自动检测激光雷达的俯仰角参数
- **测试数据管理**：CSV格式的测试数据存储和管理
- **MES数据上传**：将测试结果批量上传到MES（制造执行系统）数据库
- **质量追溯**：完整的测试数据追溯和质量管控

本手册主要介绍 **MES数据上传功能** 的使用方法。该功能具备智能异常处理、重复数据去重、断点续传等特性，确保数据上传的可靠性和完整性。

## 2. 系统要求

### 2.1 硬件要求
- CPU: Intel i3或同等性能处理器
- 内存: 4GB RAM或以上
- 硬盘: 至少100MB可用空间
- 网络: 稳定的局域网连接

### 2.2 软件要求
- 操作系统: Windows 7/10/11
- .NET Framework 4.5或以上
- ODBC数据库驱动程序

## 3. 配置说明

### 3.1 MES配置文件 (MesInfo.xml)

配置文件位置：程序根目录下的`MesInfo.xml`

```xml
<?xml version="1.0" encoding="UTF-8"?>
<MesInfo>
    <!-- 生产信息配置 -->
    <workOrder>WO202501001</workOrder>          <!-- 工单号 -->
    <userID>operator01</userID>                 <!-- 操作员ID -->
    <preop>OP010</preop>                        <!-- 上道工序号 -->
    <op>OP020</op>                              <!-- 当前工序号 -->
    <station>ST001</station>                    <!-- 工站名称 -->
    
    <!-- 版本信息配置 -->
    <firmwareVersion>V1.2.3</firmwareVersion>   <!-- 固件版本 -->
    <snHardwareVersion>HW_V1.0</snHardwareVersion> <!-- 硬件版本 -->
    <snFirmwareVersion>SW_V2.1</snFirmwareVersion>  <!-- 软件版本 -->
    
    <!-- 数据库连接配置 -->
    <dataSourceName>MES_DB</dataSourceName>     <!-- 数据源名称 -->
    <hostName>*************</hostName>          <!-- 数据库服务器IP -->
    <port>1433</port>                           <!-- 数据库端口 -->
    <dataBaseUserID>mes_user</dataBaseUserID>   <!-- 数据库用户名 -->
    <password>mes_password</password>           <!-- 数据库密码 -->
</MesInfo>
```

### 3.2 配置项说明

| 配置项 | 必填 | 说明 | 示例值 |
|--------|------|------|--------|
| workOrder | ✓ | 当前生产工单号 | WO202501001 |
| userID | ✓ | 操作员工号 | operator01 |
| op | ✓ | 当前工序号 | OP020 |
| station | ✓ | 工站标识 | ST001 |
| firmwareVersion | ✓ | 期望的固件版本 | V1.2.3 |
| hostName | ✓ | MES数据库服务器地址 | ************* |
| port | ✓ | 数据库端口 | 1433 |
| dataBaseUserID | ✓ | 数据库用户名 | mes_user |
| password | ✓ | 数据库密码 | mes_password |

## 4. 使用说明

### 4.1 数据文件准备

#### 4.1.1 CSV文件格式要求
测试数据文件必须为CSV格式，包含以下列（按顺序）：

| 列序号 | 列名 | 数据类型 | 说明 | 示例 |
|--------|------|----------|------|------|
| 1 | 电机标签 | 字符串 | 电机标签号（自动补零到8位） | 12345 |
| 2 | MCUID | 字符串 | MCU唯一标识 | MCU001234 |
| 3 | 固件版本 | 字符串 | 固件版本号 | V1.2.3 |
| 4 | 测试结果 | 整数 | 测试结果（1=通过，0=失败） | 1 |
| 5 | 最小俯仰角 | 浮点数 | 测试得到的最小俯仰角 | -15.5 |
| 6 | 最大俯仰角 | 浮点数 | 测试得到的最大俯仰角 | 15.2 |
| 7 | 俯仰角下限 | 浮点数 | 标准下限值 | -16.0 |
| 8 | 俯仰角上限 | 浮点数 | 标准上限值 | 16.0 |

#### 4.1.2 文件示例
```csv
12345,MCU001234,V1.2.3,1,-15.5,15.2,-16.0,16.0
12346,MCU001235,V1.2.3,0,-17.1,14.8,-16.0,16.0
12347,MCU001236,V1.2.3,1,-15.8,15.5,-16.0,16.0
```

### 4.2 操作步骤

#### 4.2.1 启动程序
1. 双击运行`UploadPitchDataApp.exe`
2. 程序启动后会自动加载配置文件
3. 检查界面上的配置信息是否正确

#### 4.2.2 选择数据文件
1. 点击"选择文件"按钮
2. 浏览并选择要上传的CSV数据文件
3. 程序会自动检测文件格式和内容

#### 4.2.3 文件处理机制
程序采用智能文件处理机制：

**原始文件保护**：
- 如果选择的是原始测试文件，程序会自动创建工作拷贝
- 工作拷贝文件名格式：`原文件名_工作副本_时间戳.csv`
- 原始文件保持不变，所有操作在工作拷贝上进行

**录入状态跟踪**：
- 工作拷贝会增加"录入状态"列
- 状态值含义：
  - `1`：已成功录入MES
  - `空值`：未录入（待处理）
  - `-1`：录入异常（需重新处理）

#### 4.2.4 数据上传
1. 点击"开始上传"按钮
2. 程序会显示处理进度和状态信息
3. 上传过程中可以查看实时统计信息：
   - 总数据量
   - 已处理数量
   - 成功上传数量
   - 失败数量
   - 异常数量

#### 4.2.5 查看结果
上传完成后，程序会生成以下结果文件：
- `mes上传成功数据表_时间戳.csv`：成功上传的数据
- `mes上传失败数据表_时间戳.csv`：上传失败的数据
- `固件版本异常数据表_时间戳.csv`：固件版本不匹配的数据
- `mes检测异常数据表_时间戳.csv`：MES信息异常的数据

## 5. 异常处理说明

### 5.1 常见异常及解决方法

#### 5.1.1 数据库连接异常
**现象**：程序提示"无法打开数据库"

**可能原因**：
- 网络连接问题
- 数据库服务器故障
- 配置信息错误
- 用户权限不足

**解决方法**：
1. 检查网络连接是否正常
2. 验证数据库服务器是否运行
3. 确认`MesInfo.xml`中的数据库配置信息
4. 联系系统管理员检查用户权限

#### 5.1.2 固件版本不匹配
**现象**：数据被记录到"固件版本异常数据表"

**可能原因**：
- CSV文件中的固件版本与配置不一致
- 测试设备固件版本错误

**解决方法**：
1. 检查测试设备的固件版本
2. 更新`MesInfo.xml`中的`firmwareVersion`配置
3. 或者更新测试设备固件到正确版本

#### 5.1.3 数据格式错误
**现象**：程序提示"CSV data error"或"CSV data col error"

**可能原因**：
- CSV文件列数不足
- 数据类型转换失败
- 文件编码问题

**解决方法**：
1. 检查CSV文件是否包含所有必需的8列数据
2. 验证数值列是否包含有效的数字
3. 确保文件使用UTF-8或GBK编码

### 5.2 断点续传功能

#### 5.2.1 软件异常退出恢复
如果程序在上传过程中异常退出：

1. **重新启动程序**
2. **选择之前的工作拷贝文件**（带有录入状态列的文件）
3. **程序会自动检测录入状态**：
   - 跳过已成功录入的数据（状态=1）
   - 重新处理未录入的数据（状态=空）
   - 重新处理异常数据（状态=-1）

#### 5.2.2 重复数据处理
程序采用智能去重机制：
- **逆序处理**：从文件底部开始处理，优先处理最新数据
- **标签去重**：同一电机标签只处理最新的一条记录
- **状态跳过**：已录入的数据自动跳过

### 5.3 错误代码说明

| 错误代码 | 含义 | 处理建议 |
|----------|------|----------|
| DB_CONN_FAIL | 数据库连接失败 | 检查网络和数据库配置 |
| VERSION_MISMATCH | 固件版本不匹配 | 更新配置或固件版本 |
| DATA_FORMAT_ERROR | 数据格式错误 | 检查CSV文件格式 |
| UPLOAD_RETRY_EXCEEDED | 上传重试次数超限 | 检查网络稳定性 |
| MES_INFO_INCOMPLETE | MES信息不完整 | 检查配置文件完整性 |

## 6. 维护与故障排除

### 6.1 日志文件
程序运行时会生成日志文件，位于程序目录的`logs`文件夹中：
- `upload_YYYYMMDD.log`：每日上传操作日志
- `error_YYYYMMDD.log`：错误信息日志

### 6.2 性能优化建议
1. **网络环境**：确保稳定的网络连接，避免网络波动
2. **数据量控制**：单次上传建议不超过1000条记录
3. **定期清理**：定期清理历史日志和结果文件
4. **数据库维护**：定期进行数据库性能优化

### 6.3 技术支持
如遇到无法解决的问题，请联系技术支持：
- 邮箱：<EMAIL>
- 电话：400-xxx-xxxx
- 提供信息：错误截图、日志文件、配置文件

---

**版本信息**：V1.0  
**更新日期**：2025-01-17  
**适用软件版本**：UploadPitchDataApp V1.0及以上
