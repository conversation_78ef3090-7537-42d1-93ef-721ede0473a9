# MES数据上传异常处理流程图

## 流程概述

本文档描述了MES数据上传过程中的异常处理流程，包括文件处理异常、重复数据处理、软件异常退出恢复等机制。

## 完整流程图

```mermaid
graph TD
    A[用户选择CSV文件] --> B{检查文件类型}
    B -->|原始文件| C[创建工作拷贝<br/>添加录入状态列]
    B -->|工作拷贝| D[直接使用现有文件]
    
    C --> E[逆序扫描数据<br/>从底部开始]
    D --> E
    
    E --> F{检查电机标签}
    F -->|已处理过| G[跳过重复数据]
    F -->|未处理| H{检查录入状态}
    
    H -->|状态=1| I[跳过已录入数据]
    H -->|状态=空或-1| J[执行数据验证]
    
    J --> K{固件版本检查}
    K -->|不匹配| L[记录到固件版本异常表<br/>状态=-1]
    K -->|匹配| M{MES信息检查}
    
    M -->|异常| N[记录到MES异常表<br/>状态=-1]
    M -->|正常| O[构建MES数据]
    
    O --> P{数据库连接}
    P -->|失败| Q[显示连接错误<br/>终止处理]
    P -->|成功| R[执行上传操作]
    
    R --> S{上传结果}
    S -->|成功| T[更新状态=1<br/>保存到成功表]
    S -->|失败| U{重试次数检查}
    
    U -->|<5次| V[重试上传]
    U -->|≥5次| W[更新状态=-1<br/>保存到失败表]
    
    V --> R
    
    T --> X[保存工作拷贝文件]
    W --> X
    L --> X
    N --> X
    I --> Y[处理下一条数据]
    G --> Y
    X --> Y
    
    Y --> Z{是否还有数据}
    Z -->|是| F
    Z -->|否| AA[生成处理报告]
    
    Q --> BB[用户手动重试]
    BB --> A
    
    AA --> CC[显示处理结果统计]
    
    style C fill:#e1f5fe
    style T fill:#c8e6c9
    style W fill:#ffcdd2
    style L fill:#ffcdd2
    style N fill:#ffcdd2
    style Q fill:#ffcdd2
```

## 关键流程说明

### 1. 文件处理流程

#### 1.1 原始文件保护
- 检测加载的文件是否为原始文件（无录入状态列）
- 如果是原始文件，自动创建工作拷贝文件
- 工作拷贝文件命名规则：`原文件名_工作副本_时间戳.csv`

#### 1.2 录入状态跟踪
- 在工作拷贝中增加"录入状态"列
  - `1`: 已成功录入MES系统
  - `空值`: 未录入（待处理）
  - `-1`: 录入异常（需重新处理）
- 每次录入操作后立即更新状态并保存文件

### 2. 数据处理流程

#### 2.1 逆序处理策略
- 从文件底部开始处理数据（最新数据优先）
- 使用电机标签作为唯一标识符
- 同一电机标签只处理最新的一条记录

#### 2.2 状态检查逻辑
- 检查每条数据的录入状态
- 跳过已成功录入的数据（状态=1）
- 处理未录入（状态=空）和异常（状态=-1）的数据

### 3. 异常处理流程

#### 3.1 数据验证异常
- 固件版本不匹配：记录到固件版本异常表，状态设为-1
- MES信息异常：记录到MES检测异常表，状态设为-1
- 数据格式异常：记录到数据格式异常表，状态设为-1

#### 3.2 上传异常处理
- 数据库连接失败：显示错误信息，允许用户重试
- 上传失败：最多重试5次，超过则记录到上传失败表，状态设为-1
- 上传成功：更新状态为1，记录到上传成功表

#### 3.3 软件异常退出恢复
- 重新启动时检查工作拷贝文件的录入状态列
- 只处理状态为空值或-1的记录
- 避免重复录入已成功的数据

## 实现建议

### 代码实现要点

```cpp
// 1. 文件类型检测
bool isWorkingCopy(const QString &fileName) {
    // 检查文件是否包含录入状态列
    QFile file(fileName);
    if (!file.open(QIODevice::ReadOnly)) {
        return false;
    }
    
    QTextStream in(&file);
    QString header = in.readLine();
    return header.contains("录入状态");
}

// 2. 创建工作拷贝
QString createWorkingCopy(const QString &originalFile) {
    QFileInfo fileInfo(originalFile);
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMddHHmmss");
    QString workingCopyName = QString("%1_工作副本_%2.%3")
        .arg(fileInfo.baseName())
        .arg(timestamp)
        .arg(fileInfo.suffix());
    
    QString workingCopyPath = fileInfo.path() + "/" + workingCopyName;
    
    // 复制文件并添加状态列
    // ...
    
    return workingCopyPath;
}

// 3. 逆序处理数据
void processDataInReverseOrder(const QString &workingCopyFile) {
    QVector<PitchData> pitchData = csvReader.readDataFromCsv(workingCopyFile);
    QSet<QString> processedNbrs;
    
    // 从后向前处理数据
    for (int i = pitchData.size() - 1; i >= 0; i--) {
        QString nbr = pitchData[i].nbr;
        int status = pitchData[i].uploadStatus;
        
        // 跳过已处理的标签
        if (processedNbrs.contains(nbr)) {
            continue;
        }
        
        // 跳过已成功录入的数据
        if (status == 1) {
            continue;
        }
        
        // 处理当前数据...
        processedNbrs.insert(nbr);
    }
}

// 4. 更新录入状态
void updateUploadStatus(const QString &workingCopyFile, int row, int status) {
    // 更新CSV文件中指定行的状态列
    // ...
    
    // 立即保存文件
    // ...
}
```

## 相关文档链接

- [[MES数据上传功能文档]]
- [[MES上传架构图]]
