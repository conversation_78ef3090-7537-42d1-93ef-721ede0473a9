
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "RC"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_RC
  "F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/version.rc" "F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build/CMakeFiles/CSPC_UploadPitchDataApp.dir/version.rc.obj"
  )

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_RC
  "MAIN_APP_WINDOW_TITLE=\"CSPC_UploadPitchDataApp_V0.0.0\""
  "QT_CORE_LIB"
  "QT_GUI_LIB"
  "QT_MESSAGELOGCONTEXT"
  "QT_NO_DEBUG"
  "QT_SERIALPORT_LIB"
  "QT_SQL_LIB"
  "QT_WIDGETS_LIB"
  )

# The include file search paths:
set(CMAKE_RC_TARGET_INCLUDE_PATH
  "."
  "../"
  "CSPC_UploadPitchDataApp_autogen/include"
  "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include"
  "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtWidgets"
  "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtGui"
  "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtANGLE"
  "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtCore"
  "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/./mkspecs/win32-g++"
  "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtSql"
  "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtSerialPort"
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build/CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp" "CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.obj" "gcc" "CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.obj.d"
  "F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build/CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp" "CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.obj" "gcc" "CMakeFiles/CSPC_UploadPitchDataApp.dir/CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp.obj.d"
  "F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/CSV/CSVReader.cpp" "CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVReader.cpp.obj" "gcc" "CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVReader.cpp.obj.d"
  "F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/CSV/CSVWriter.cpp" "CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVWriter.cpp.obj" "gcc" "CMakeFiles/CSPC_UploadPitchDataApp.dir/CSV/CSVWriter.cpp.obj.d"
  "F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/Comm.cpp" "CMakeFiles/CSPC_UploadPitchDataApp.dir/Comm.cpp.obj" "gcc" "CMakeFiles/CSPC_UploadPitchDataApp.dir/Comm.cpp.obj.d"
  "F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/Config/ConfigLoader.cpp" "CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/ConfigLoader.cpp.obj" "gcc" "CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/ConfigLoader.cpp.obj.d"
  "F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/Config/XmlConfig.cpp" "CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/XmlConfig.cpp.obj" "gcc" "CMakeFiles/CSPC_UploadPitchDataApp.dir/Config/XmlConfig.cpp.obj.d"
  "F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/DataStore/DataStore.cpp" "CMakeFiles/CSPC_UploadPitchDataApp.dir/DataStore/DataStore.cpp.obj" "gcc" "CMakeFiles/CSPC_UploadPitchDataApp.dir/DataStore/DataStore.cpp.obj.d"
  "F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/MainForm.cpp" "CMakeFiles/CSPC_UploadPitchDataApp.dir/MainForm.cpp.obj" "gcc" "CMakeFiles/CSPC_UploadPitchDataApp.dir/MainForm.cpp.obj.d"
  "F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/Pojo/MESData.cpp" "CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/MESData.cpp.obj" "gcc" "CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/MESData.cpp.obj.d"
  "F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/Pojo/PitchData.cpp" "CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/PitchData.cpp.obj" "gcc" "CMakeFiles/CSPC_UploadPitchDataApp.dir/Pojo/PitchData.cpp.obj.d"
  "F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/main.cpp" "CMakeFiles/CSPC_UploadPitchDataApp.dir/main.cpp.obj" "gcc" "CMakeFiles/CSPC_UploadPitchDataApp.dir/main.cpp.obj.d"
  "F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/widget.cpp" "CMakeFiles/CSPC_UploadPitchDataApp.dir/widget.cpp.obj" "gcc" "CMakeFiles/CSPC_UploadPitchDataApp.dir/widget.cpp.obj.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
