#ifndef CSVREADER_H
#define CSVREADER_H

#include "Pojo/PitchData.h"
#include <QDateTime>
#include <QFileInfo>
#include <QObject>
#include <QVector>


class CSVReader : public QObject {
    Q_OBJECT
  public:
    explicit CSVReader(QObject *parent = nullptr);

    // 原有方法
    QVector<PitchData> readDataFromCsv(const QString &fileName);

    // 新增方法：文件处理
    bool    isWorkingCopy(const QString &fileName);
    QString createWorkingCopy(const QString &originalFile);
    QString getWorkingCopyPath(const QString &originalFile);

    // 新增方法：状态管理
    QVector<PitchData> readDataWithStatus(const QString &fileName);
    bool               updateUploadStatus(const QString &fileName, int rowIndex, UploadStatus status);
    bool               updateUploadStatus(const QString &fileName, const QString &nbr, UploadStatus status);

    // 新增方法：数据处理
    QVector<PitchData> filterNeedsProcessing(const QVector<PitchData> &data);
    QVector<PitchData> removeDuplicatesKeepLatest(const QVector<PitchData> &data);

  signals:
    void workingCopyCreated(const QString &workingCopyPath);
    void statusUpdated(const QString &nbr, UploadStatus status);

  private:
    // 私有辅助方法
    UploadStatus parseUploadStatus(const QString &statusStr);
    QString      uploadStatusToString(UploadStatus status);
    bool         addStatusColumnToFile(const QString &fileName);
    QStringList  readAllLines(const QString &fileName);
    bool         writeAllLines(const QString &fileName, const QStringList &lines);
};

#endif  // CSVREADER_H
