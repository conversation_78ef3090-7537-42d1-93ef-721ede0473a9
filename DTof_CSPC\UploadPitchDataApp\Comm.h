#ifndef COMM_H
#define COMM_H

#include <QAbstractNativeEventFilter>
#include <QTimer>
#include <QWidget>
#include <QtSerialPort/QSerialPort>


namespace Ui {
class Comm;
}

enum class CmdType {
    NoCmd = 0,
    MCUID,
    SoftwareVersion,
    eLIDAR_RUNNING,
};

class Comm : public QWidget, public QAbstractNativeEventFilter {
    Q_OBJECT

  public:
    explicit Comm(QWidget *parent = nullptr);
    ~Comm();

    bool nativeEventFilter(const QByteArray &eventType, void *message, long *result) override;

  private slots:
    void on_virtualUartBtn_clicked();
    void on_lidarUartBtn_clicked();

    void parseVirtualDataSlot(const QString &data);
    void parseLidarDataSlot(QByteArray data);

  signals:
    void parseVirtualData(const QString &data);
    void parseLidarData(QByteArray data);

    void parsed(const QString &info);

  private:
    void updatePort();

  private:
    Ui::Comm *ui;

    QSerialPort virtualSerial;
    QSerialPort lidarSerial;

    CmdType type{CmdType::NoCmd};

    void parseDataMcuId(QByteArray &data);
    void parseDataSoftwareVersion(QByteArray &data);
    void parseDataCommonAck(QByteArray &data);

    QTimer *timer{nullptr};

    bool isValidForIndex(int index, int len) {
        if (index <= len)
            return true;
        else
            return false;
    }

    QString toHexString(const QByteArray &byteArray) {
        QString text;
        text.clear();
        for (const auto &item : byteArray) {
            text += QString("%1").arg(static_cast<quint8>(item), 2, 16, QLatin1Char('0')).toUpper();
        }
        return text;
    }
};

#endif  // COMM_H
