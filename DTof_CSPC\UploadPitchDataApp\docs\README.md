# CSPC俯仰角数据上传应用

## 项目概述

本应用用于将俯仰角视觉检测的测试数据上传到MES（制造执行系统）数据库。系统读取CSV格式的测试数据，进行验证和转换后，批量上传到MES数据库。

## 功能特性

- ✅ CSV文件数据读取和解析
- ✅ 数据格式验证和错误检测
- ✅ MES数据结构转换
- ✅ 数据库批量上传
- ✅ 重试机制和错误处理
- ✅ 详细的结果报告生成
- ✅ 标签关联自动更新

## 系统要求

- **操作系统**: Windows 10/11
- **开发环境**: Qt 5.14.2
- **编译器**: MinGW 7.3.0 64-bit
- **数据库**: 支持ODBC的MES数据库
- **依赖库**: Qt Core, Qt GUI, Qt Widgets, Qt SQL, Qt SerialPort

## 项目结构

```
DTof_CSPC/UploadPitchDataApp/
├── main.cpp                    # 应用程序入口
├── widget.h/cpp/ui            # 主界面控制器
├── Config/                    # 配置管理
│   ├── ConfigLoader.h/cpp     # 配置加载器
│   └── XmlConfig.h/cpp        # XML配置解析
├── CSV/                       # CSV文件处理
│   ├── CSVReader.h/cpp        # CSV读取器
│   └── CSVWriter.h/cpp        # CSV输出器
├── DataStore/                 # 数据访问层
│   └── DataStore.h/cpp        # 数据库操作
├── Pojo/                      # 数据模型
│   ├── PitchData.h/cpp        # 俯仰角数据模型
│   └── MESData.h/cpp          # MES数据模型
├── build/                     # 构建输出
│   └── bin/
│       └── MesInfo.xml        # MES配置文件
├── docs/                      # 项目文档
└── scriptFile/                # 脚本文件
```

## 快速开始

### 1. 环境配置

确保已安装Qt 5.14.2开发环境和MinGW编译器。

### 2. 编译项目

```bash
# 创建构建目录
mkdir build && cd build

# 生成Makefile
cmake ..

# 编译项目
make
```

### 3. 配置MES连接

编辑 `build/bin/MesInfo.xml` 文件：

```xml
<MesInfo>
    <workOrder>工单号</workOrder>
    <userID>用户ID</userID>
    <op>工序号</op>
    <station>工站名称</station>
    <dataSourceName>数据源名称</dataSourceName>
    <hostName>数据库主机地址</hostName>
    <port>数据库端口</port>
    <dataBaseUserID>数据库用户名</dataBaseUserID>
    <password>数据库密码</password>
</MesInfo>
```

### 4. 运行应用

```bash
./CSPC_UploadPitchDataApp.exe
```

## 使用说明

1. **选择文件**: 点击"选择上传文件"按钮，选择包含俯仰角测试数据的CSV文件
2. **数据验证**: 系统自动验证数据格式和完整性
3. **上传处理**: 系统将验证通过的数据转换为MES格式并上传
4. **查看结果**: 检查生成的报告文件了解处理结果

## CSV文件格式

输入的CSV文件应包含以下列：

| 列名 | 说明 | 示例 |
|------|------|------|
| 时间 | 测试时间 | 2025/06/23 14:38:33 |
| 电机标签 | 8位电机标签号 | 01335856 |
| MCUID | MCU标识符 | 360513503035573430097CAF |
| 固件版本 | 固件版本号 | V1.**********.2.25 |
| 测试结果 | 测试结果(0/1) | 1 |
| 最小俯仰角 | 测试最小值 | 1.297428 |
| 最大俯仰角 | 测试最大值 | 2.583963 |
| 俯仰角下限 | 标准下限 | 0.000000 |
| 俯仰角上限 | 标准上限 | 5.000000 |
| 测试数据 | 原始测试数据 | 1.734;1.429;1.297... |

## 输出文件

系统会在以下目录生成结果文件：

- `固件版本异常数据表/` - 固件版本不匹配的数据
- `mes检测异常数据表/` - MES信息异常的数据  
- `mes上传成功数据表/` - 成功上传的数据
- `mes上传失败数据表/` - 上传失败的数据

## 开发文档

详细的开发文档请参考：

- [MES数据上传功能文档](mes/MES数据上传功能文档.md)
- [MES上传架构图](mes/MES上传架构图.md)

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查网络连接
   - 验证MesInfo.xml配置
   - 确认数据库服务状态

2. **CSV文件格式错误**
   - 检查文件编码(UTF-8)
   - 验证列数和数据类型
   - 确认必需字段完整

3. **上传失败**
   - 查看失败数据报告
   - 检查数据库权限
   - 验证数据格式

### 日志查看

应用程序会输出详细的调试信息，可通过控制台查看运行状态和错误信息。

## 版本历史

- **v0.0.0** - 初始版本，基本的MES数据上传功能

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用内部许可证，仅供公司内部使用。