#ifndef XMLCONFIG_H
#define XMLCONFIG_H

#include <QString>



class XmlConfig
{
public:
    XmlConfig();

    // 获取工单
    QString getWorkOrder() const
    {
        return this->workOrder;
    }
    // 设置工单
    void setWorkOrder(const QString &value)
    {
        this->workOrder = value;
    }

    // 获取用户ID
    QString getUserID() const
    {
        return this->userID;
    }
    // 设置用户ID
    void setUserID(const QString &value)
    {
        this->userID = value;
    }

    // 获取前置操作
    QString getPreop() const
    {
        return this->preop;
    }
    // 设置前置操作
    void setPreop(const QString &value)
    {
        this->preop  = value;
    }

    // 获取操作
    QString getOp() const
    {
        return this->op;
    }
    // 设置操作
    void setOp(const QString &value)
    {
        this->op  = value;
    }

    // 获取站点
    QString getStation() const
    {
        return this->station;
    }
    // 设置站点
    void setStation(const QString &value)
    {
        this->station = value;
    }

    // 获取固件版本
    QString getFirmwareVersion() const
    {
        return this->firmwareVersion;
    }
    // 设置固件版本
    void setFirmwareVersion(const QString &value)
    {
        this->firmwareVersion  = value;
    }

    // 获取标签硬件版本序列号
    QString getSnHardwareVersion() const
    {
        return this->snHardwareVersion;
    }
    // 设置标签硬件版本序列号
    void setSnHardwareVersion(const QString &value)
    {
        this->snHardwareVersion = value;
    }

    // 获取标签软件版本序列号
    QString getSnFirmwareVersion() const
    {
        return this->snFirmwareVersion;
    }
    // 设置标签软件版本序列号
    void setSnFirmwareVersion(const QString &value)
    {
        this->snFirmwareVersion  = value;
    }

    // 获取数据源名称
    QString getDataSourceName() const
    {
        return this->dataSourceName;
    }
    // 设置数据源名称
    void setDataSourceName(const QString &value)
    {
        this->dataSourceName = value;
    }

    // 获取主机名
    QString getHostName() const
    {
        return this->hostName;
    }
    // 设置主机名
    void setHostName(const QString &value)
    {
        this->hostName  = value;
    }

    // 获取端口
    QString getPort() const
    {
        return this->port;
    }
    // 设置端口
    void setPort(const QString &value)
    {
        this->port  = value;
    }

    // 获取数据库用户ID
    QString getDataBaseUserID() const
    {
        return this->dataBaseUserID;
    }
    // 设置数据库用户ID
    void setDataBaseUserID(const QString &value)
    {
        this->dataBaseUserID  = value;
    }

    // 获取密码
    QString getPassword() const
    {
        return this->password;
    }
    // 设置密码
    void setPassword(const QString &value)
    {
        this->password = value;
    }


private:

    QString workOrder{"0000"};
    QString userID{"000081"};
    QString preop{"109"};
    QString op{"110"};
    QString station{"默认工站"};
    QString firmwareVersion{"V1.********.2"};
    QString snHardwareVersion{"1"};
    QString snFirmwareVersion{"1"};
    QString dataSourceName{"hyh-new"};
    QString hostName{"*************"};
    QString port{"56398"};
    QString dataBaseUserID{"mfg"};
    QString password{"mfg#2024"};

};

#endif // XMLCONFIG_H
