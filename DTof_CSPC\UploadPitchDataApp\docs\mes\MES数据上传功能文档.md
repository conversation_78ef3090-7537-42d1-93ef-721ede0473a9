# MES数据上传功能文档

## 功能概述

本功能负责将俯仰角视觉检测的测试数据上传到MES（制造执行系统）数据库。系统读取CSV格式的测试数据，进行数据验证和转换，然后批量上传到MES数据库的`pub.xsub6_det`表中。

## 系统架构

### 核心组件

1. **Widget (主控制器)**
   - 协调整个数据上传流程
   - 处理用户界面交互
   - 管理数据库连接

2. **CSVReader (CSV读取器)**
   - 读取俯仰角测试数据CSV文件
   - 数据格式验证和转换
   - 返回`PitchData`对象集合

3. **DataStore (数据存储层)**
   - 数据库连接管理
   - MES数据上传操作
   - 标签关联更新

4. **MESData (数据模型)**
   - MES上传数据结构定义
   - 包含所有必需的MES字段

5. **CSVWriter (结果输出)**
   - 记录上传成功/失败的数据
   - 生成错误报告

## 数据流程

### 1. 数据读取阶段
```
CSV文件 → CSVReader.readDataFromCsv() → QVector<PitchData>
```

**CSV文件格式：**
- 时间,电机标签,MCUID,固件版本,测试结果,最小俯仰角,最大俯仰角,俯仰角下限,俯仰角上限,测试数据

### 2. 数据验证阶段
- 固件版本检查
- MES信息完整性验证
- 数据格式校验

### 3. MES数据构建阶段
```cpp
// 核心数据映射
MESData target;
target.domain = domainLocal;        // 域
target.nbr = nbrLocal;             // 电机标签号
target.op = opLocal;               // 工序号
target.userID = userIDLocal;       // 用户ID
target.date = QDate::currentDate().toString("yyyy/MM/dd");
target.time = hours * 3600 + minute * 60 + second;
target.testResult = 测试结果;
target.rsnCode = 原因代码;
target.station = 工站信息;
target.topNbr = 主板标签;
target.workOrder = 工单号;
target.mcuID = MCU ID;
target.projrct1 = "MIN";
target.stand1 = 俯仰角下限;
target.act1 = 最小俯仰角;
target.hege1 = 是否合格;
target.projrct2 = "MAX";
target.stand2 = 俯仰角上限;
target.act2 = 最大俯仰角;
target.hege2 = 是否合格;
```

### 4. 数据库上传阶段
```cpp
// 上传流程
for (MESData &item : mesData) {
    // 1. 标签关联更新（如需要）
    if (item.isNeedUpdateTopNbr) {
        dataStore.updataTopNbrForNbr(odbcDB, item.nbr, item.topNbr, item.domain);
    }
    
    // 2. 获取事务号（重试机制）
    for (tryCnt = 0; tryCnt < 5; tryCnt++) {
        dataStore.findXsubTrnbrMax(odbcDB, "xsub6", trnbrLocal);
        item.trnbr = trnbrLocal;
        
        // 3. 执行数据上传
        if (dataStore.uploadMesData(odbcDB, item) == NoError) {
            uploadMessuccessData.push_back(item);
            break;
        }
    }
    
    // 4. 记录失败数据
    if (tryCnt >= 5) {
        uploadMesErrorData.push_back(item);
    }
}
```

## 数据库表结构

### 目标表：pub.xsub6_det

| 字段名            | 数据类型    | 说明               | 对应MESData字段      |
| -------------- | ------- | ---------------- | ---------------- |
| xsub6_domain   | VARCHAR | 域                | domain           |
| xsub6_trnbr    | INT     | 事务号              | trnbr            |
| xsub6_nbr      | VARCHAR |                  | nbr              |
| xsub6_op       | VARCHAR | 工序号              | op               |
| xsub6_userid   | VARCHAR | 用户ID             | userID           |
| xsub6_date     | VARCHAR | 日期               | date             |
| xsub6_time     | INT     | 时间               | time             |
| xsub6_rslt     | INT     | 测试结果(TRUE/FALSE) | testResult       |
| xsub6_rsn_code | VARCHAR | 原因代码(PASS/NG)    | rsnCode          |
| xsub6__chr03   | VARCHAR | 工站               | station          |
| xsub6__chr04   | VARCHAR | 主板标签(虚拟标签)       | topNbr           |
| xsub6_newid    | VARCHAR | 工单号              | workOrder        |
| xsub6_uid      | VARCHAR | MCU ID           | mcuID            |
| xsub6_project1 | VARCHAR | 项目1              | projrct1 ("MIN") |
| xsub6_stand1   | VARCHAR | 标准1              | stand1           |
| xsub6_act1     | FLOAT   | 实际值1             | act1             |
| xsub6_hege1    | INT     | 合格1              | hege1            |
| xsub6_project2 | VARCHAR | 项目2              | projrct2 ("MAX") |
| xsub6_stand2   | VARCHAR | 标准2              | stand2           |
| xsub6_act2     | FLOAT   | 实际值2             | act2             |
| xsub6_hege2    | INT     | 合格2              | hege2            |

## 配置文件

### MesInfo.xml 配置项
```xml
<MesInfo>
    <workOrder>工单号</workOrder>
    <userID>用户ID</userID>
    <preop>上道工序</preop>
    <op>当前工序</op>
    <station>工站名称</station>
    <firmwareVersion>固件版本</firmwareVersion>
    <snHardwareVersion>硬件版本</snHardwareVersion>
    <snFirmwareVersion>软件版本</snFirmwareVersion>
    <dataSourceName>数据源名称</dataSourceName>
    <hostName>数据库主机</hostName>
    <port>数据库端口</port>
    <dataBaseUserID>数据库用户</dataBaseUserID>
    <password>数据库密码</password>
</MesInfo>
```

## 错误处理机制

### 1. 数据验证错误
- **固件版本异常**: 记录到`固件版本异常数据表`
- **MES信息异常**: 记录到`mes检测异常数据表`

### 2. 上传重试机制
- 每条数据最多重试5次
- 重试包括：获取事务号 + 数据上传
- 失败后记录到`mes上传失败数据表`

### 3. 结果记录
- **成功数据**: 保存到`mes上传成功数据表`
- **失败数据**: 保存到`mes上传失败数据表`

## 关键算法

### 1. 合格判定算法
```cpp
bool Widget::isHege(float value, float min, float max) {
    return (value >= min && value <= max) ? true : false;
}
```

### 2. 时间转换算法
```cpp
// 将当前时间转换为秒数
uint8_t hours = QTime::currentTime().toString("H").toUInt();
uint8_t minute = QTime::currentTime().toString("m").toUInt();
uint8_t second = QTime::currentTime().toString("s").toUInt();
target.time = hours * 3600 + minute * 60 + second;
```

## 性能考虑

1. **批量处理**: 一次性读取所有CSV数据后批量上传
2. **事务管理**: 每条记录独立事务，避免全部回滚
3. **重试机制**: 网络异常时自动重试，提高成功率
4. **异步处理**: 使用信号槽机制，避免界面阻塞

## 使用说明

1. 确保`MesInfo.xml`配置正确
2. 选择包含俯仰角测试数据的CSV文件
3. 点击上传按钮开始处理
4. 查看处理结果和生成的报告文件

## 注意事项

1. CSV文件必须包含完整的测试数据列
2. 数据库连接必须正常
3. 固件版本必须与配置匹配
4. 电机标签号会自动补零到8位