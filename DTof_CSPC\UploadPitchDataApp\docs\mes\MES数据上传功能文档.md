# MES数据上传功能文档

## 功能概述

本功能负责将俯仰角视觉检测的测试数据上传到MES（制造执行系统）数据库。系统读取CSV格式的测试数据，进行数据验证和转换，然后批量上传到MES数据库的`pub.xsub6_det`表中。

## 系统架构

### 核心组件

1. **Widget (主控制器)**
   - 协调整个数据上传流程
   - 处理用户界面交互
   - 管理数据库连接

2. **CSVReader (CSV读取器)**
   - 读取俯仰角测试数据CSV文件
   - 数据格式验证和转换
   - 返回`PitchData`对象集合

3. **DataStore (数据存储层)**
   - 数据库连接管理
   - MES数据上传操作
   - 标签关联更新

4. **MESData (数据模型)**
   - MES上传数据结构定义
   - 包含所有必需的MES字段

5. **CSVWriter (结果输出)**
   - 记录上传成功/失败的数据
   - 生成错误报告

## 数据流程

### 1. 数据读取阶段
```
CSV文件 → CSVReader.readDataFromCsv() → QVector<PitchData>
```

**CSV文件格式：**
- 时间,电机标签,MCUID,固件版本,测试结果,最小俯仰角,最大俯仰角,俯仰角下限,俯仰角上限,测试数据

### 2. 数据验证阶段
- 固件版本检查
- MES信息完整性验证
- 数据格式校验

### 3. MES数据构建阶段
```cpp
// 核心数据映射
MESData target;
target.domain = domainLocal;        // 域
target.nbr = nbrLocal;             // 电机标签号
target.op = opLocal;               // 工序号
target.userID = userIDLocal;       // 用户ID
target.date = QDate::currentDate().toString("yyyy/MM/dd");
target.time = hours * 3600 + minute * 60 + second;
target.testResult = 测试结果;
target.rsnCode = 原因代码;
target.station = 工站信息;
target.topNbr = 主板标签;
target.workOrder = 工单号;
target.mcuID = MCU ID;
target.projrct1 = "MIN";
target.stand1 = 俯仰角下限;
target.act1 = 最小俯仰角;
target.hege1 = 是否合格;
target.projrct2 = "MAX";
target.stand2 = 俯仰角上限;
target.act2 = 最大俯仰角;
target.hege2 = 是否合格;
```

### 4. 数据库上传阶段
```cpp
// 上传流程
for (MESData &item : mesData) {
    // 1. 标签关联更新（如需要）
    if (item.isNeedUpdateTopNbr) {
        dataStore.updataTopNbrForNbr(odbcDB, item.nbr, item.topNbr, item.domain);
    }
    
    // 2. 获取事务号（重试机制）
    for (tryCnt = 0; tryCnt < 5; tryCnt++) {
        dataStore.findXsubTrnbrMax(odbcDB, "xsub6", trnbrLocal);
        item.trnbr = trnbrLocal;
        
        // 3. 执行数据上传
        if (dataStore.uploadMesData(odbcDB, item) == NoError) {
            uploadMessuccessData.push_back(item);
            break;
        }
    }
    
    // 4. 记录失败数据
    if (tryCnt >= 5) {
        uploadMesErrorData.push_back(item);
    }
}
```

## 数据库表结构

### 目标表：pub.xsub6_det

| 字段名            | 数据类型    | 说明               | 对应MESData字段      |
| -------------- | ------- | ---------------- | ---------------- |
| xsub6_domain   | VARCHAR | 域                | domain           |
| xsub6_trnbr    | INT     | 事务号              | trnbr            |
| xsub6_nbr      | VARCHAR |                  | nbr              |
| xsub6_op       | VARCHAR | 工序号              | op               |
| xsub6_userid   | VARCHAR | 用户ID             | userID           |
| xsub6_date     | VARCHAR | 日期               | date             |
| xsub6_time     | INT     | 时间               | time             |
| xsub6_rslt     | INT     | 测试结果(TRUE/FALSE) | testResult       |
| xsub6_rsn_code | VARCHAR | 原因代码(PASS/NG)    | rsnCode          |
| xsub6__chr03   | VARCHAR | 工站               | station          |
| xsub6__chr04   | VARCHAR | 主板标签(虚拟标签)       | topNbr           |
| xsub6_newid    | VARCHAR | 工单号              | workOrder        |
| xsub6_uid      | VARCHAR | MCU ID           | mcuID            |
| xsub6_project1 | VARCHAR | 项目1              | projrct1 ("MIN") |
| xsub6_stand1   | VARCHAR | 标准1              | stand1           |
| xsub6_act1     | FLOAT   | 实际值1             | act1             |
| xsub6_hege1    | INT     | 合格1              | hege1            |
| xsub6_project2 | VARCHAR | 项目2              | projrct2 ("MAX") |
| xsub6_stand2   | VARCHAR | 标准2              | stand2           |
| xsub6_act2     | FLOAT   | 实际值2             | act2             |
| xsub6_hege2    | INT     | 合格2              | hege2            |

## 配置文件

### MesInfo.xml 配置项
```xml
<MesInfo>
    <workOrder>工单号</workOrder>
    <userID>用户ID</userID>
    <preop>上道工序</preop>
    <op>当前工序</op>
    <station>工站名称</station>
    <firmwareVersion>固件版本</firmwareVersion>
    <snHardwareVersion>硬件版本</snHardwareVersion>
    <snFirmwareVersion>软件版本</snFirmwareVersion>
    <dataSourceName>数据源名称</dataSourceName>
    <hostName>数据库主机</hostName>
    <port>数据库端口</port>
    <dataBaseUserID>数据库用户</dataBaseUserID>
    <password>数据库密码</password>
</MesInfo>
```

## 异常处理机制

### 1. 文件处理异常

#### 1.1 原始文件保护机制
- **原理**: 遵循单一来源原则，保护原始测试数据不被修改
- **实现**:
  - 检测加载的文件是否为原始文件（无录入状态列）
  - 如果是原始文件，自动创建工作拷贝文件
  - 工作拷贝文件命名规则：`原文件名_工作副本_时间戳.csv`

#### 1.2 录入状态跟踪机制
- **状态列定义**: 在工作拷贝中增加"录入状态"列
  - `1`: 已成功录入MES系统
  - `空值`: 未录入（待处理）
  - `-1`: 录入异常（需重新处理）
- **状态持久化**: 每次录入操作后立即更新状态并保存文件

### 2. 重复数据处理异常

#### 2.1 重复测试数据策略
- **问题**: 同一电机标签可能存在多次测试记录
- **解决方案**:
  - 采用逆序处理（从文件底部开始）
  - 优先处理最新的测试数据
  - 跳过已录入的历史数据

#### 2.2 数据去重算法
```cpp
// 伪代码：重复数据处理逻辑
QSet<QString> processedNbrs;  // 已处理的电机标签集合
for (int i = pitchData.size() - 1; i >= 0; i--) {  // 逆序遍历
    QString nbr = pitchData[i].nbr;
    if (processedNbrs.contains(nbr)) {
        continue;  // 跳过重复的电机标签
    }
    processedNbrs.insert(nbr);
    // 处理当前数据...
}
```

### 3. 软件异常退出恢复机制

#### 3.1 中断恢复策略
- **问题**: 软件异常退出导致录入过程中断
- **解决方案**:
  - 重新启动时检查工作拷贝文件的录入状态列
  - 只处理状态为空值或-1的记录
  - 避免重复录入已成功的数据

#### 3.2 状态检查流程
详细的异常处理流程请参考：[[MES数据上传异常处理流程图]]

核心处理逻辑：
1. **文件类型检测** → 原始文件自动创建工作拷贝
2. **逆序数据扫描** → 优先处理最新测试数据
3. **重复数据去重** → 同一标签只处理最新记录
4. **状态智能判断** → 跳过已录入，重试异常数据
5. **实时状态更新** → 每次操作后立即保存状态

### 4. 数据验证异常

#### 4.1 输入数据验证
- **固件版本异常**: 记录到`固件版本异常数据表`
- **MES信息异常**: 记录到`mes检测异常数据表`
- **数据格式异常**: 记录到`数据格式异常表`

#### 4.2 业务逻辑验证
- **电机标签格式**: 自动补零到8位，验证格式正确性
- **测试数据范围**: 验证俯仰角数值在合理范围内
- **关联关系验证**: 检查电机标签与主板标签的关联关系

### 5. 网络与数据库异常

#### 5.1 连接异常处理
- **数据库连接失败**:
  - 显示明确错误信息
  - 提供重试机制
  - 保存未处理数据状态

#### 5.2 上传重试机制
- **重试策略**: 每条数据最多重试5次
- **重试范围**: 获取事务号 + 数据上传
- **失败处理**:
  - 标记录入状态为-1
  - 记录到`mes上传失败数据表`
  - 保留详细错误信息

### 6. 结果记录与追溯

#### 6.1 操作日志记录
- **成功记录**: 保存到`mes上传成功数据表`
- **失败记录**: 保存到`mes上传失败数据表`
- **异常记录**: 保存到对应的异常数据表

#### 6.2 可追溯性保证
- **文件版本管理**: 保留原始文件和工作拷贝
- **操作时间戳**: 记录每次操作的精确时间
- **状态变更历史**: 跟踪录入状态的变更过程

## 关键算法

### 1. 合格判定算法
```cpp
bool Widget::isHege(float value, float min, float max) {
    return (value >= min && value <= max) ? true : false;
}
```

### 2. 时间转换算法
```cpp
// 将当前时间转换为秒数
uint8_t hours = QTime::currentTime().toString("H").toUInt();
uint8_t minute = QTime::currentTime().toString("m").toUInt();
uint8_t second = QTime::currentTime().toString("s").toUInt();
target.time = hours * 3600 + minute * 60 + second;
```

## 性能考虑

1. **批量处理**: 一次性读取所有CSV数据后批量上传
2. **事务管理**: 每条记录独立事务，避免全部回滚
3. **重试机制**: 网络异常时自动重试，提高成功率
4. **异步处理**: 使用信号槽机制，避免界面阻塞

## 使用说明

1. 确保`MesInfo.xml`配置正确
2. 选择包含俯仰角测试数据的CSV文件
3. 点击上传按钮开始处理
4. 查看处理结果和生成的报告文件

## 注意事项

1. CSV文件必须包含完整的测试数据列
2. 数据库连接必须正常
3. 固件版本必须与配置匹配
4. 电机标签号会自动补零到8位