import os
import sys
import re
from datetime import datetime

def get_latest_version(changelog_path):
    if not os.path.exists(changelog_path):
        return "0.0.0"
    with open(changelog_path, 'r', encoding='utf-8') as f:
        for line in f:
            match = re.match(r'^## \[v(\d+\.\d+\.\d+)\]', line)
            if match:
                return match.group(1)
    return "0.0.0"

def bump_version(version, level):
    major, minor, patch = map(int, version.split('.'))
    if level == "major":
        major += 1
        minor = 0
        patch = 0
    elif level == "minor":
        minor += 1
        patch = 0
    elif level == "patch":
        patch += 1
    return f"{major}.{minor}.{patch}"

def get_commit_level(commit_messages):
    levels = [get_commit_level_single(msg) for msg in commit_messages]
    if "major" in levels:
        return "major"
    elif "minor" in levels:
        return "minor"
    elif "patch" in levels:
        return "patch"
    return "patch"

def get_commit_level_single(commit_message):
    if "BREAKING CHANGE" in commit_message or "feat!" in commit_message:
        return "major"
    elif "feat" in commit_message:
        return "minor"
    elif "fix" in commit_message:
        return "patch"
    return "patch"

def generate_changelog(program_dir, commit_message):
    changelog_path = os.path.join(program_dir, 'CHANGELOG.md')
    latest_version = get_latest_version(changelog_path)
    level = get_commit_level(commit_message)
    new_version = bump_version(latest_version, level)
    date_str = datetime.now().strftime('%Y-%m-%d')

    changelog_entry = f"\n## [v{new_version}] - {date_str}\n" + "\n".join([f"- {msg}" for msg in commit_messages]) + "\n"

    if os.path.exists(changelog_path):
        with open(changelog_path, 'r', encoding='utf-8') as f:
            changelog_content = f.read()
        changelog_content = changelog_entry + changelog_content
    else:
        changelog_content = changelog_entry

    with open(changelog_path, 'w', encoding='utf-8') as f:
        f.write(changelog_content)
    
    return new_version


if __name__ == "__main__":
    program_dir = sys.argv[1]
    commit_messages = sys.argv[2:]

    if not commit_messages:
        print(f"No commit messages provided for {program_dir}.")
        sys.exit(1)

    new_version = generate_changelog(program_dir, commit_messages)
    print(f"New version: {new_version}")
