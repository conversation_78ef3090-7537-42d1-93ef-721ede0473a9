{"artifacts": [{"path": "bin/CSPC_UploadPitchDataApp.exe"}, {"path": "bin/CSPC_UploadPitchDataApp.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "add_definitions", "ADD_DEFINITIONS"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 144, "parent": 0}, {"command": 1, "file": 0, "line": 150, "parent": 0}, {"command": 2, "file": 0, "line": 105, "parent": 0}, {"command": 3, "file": 0, "line": 31, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -finput-charset=UTF-8 -Os -DNDEBUG"}, {"fragment": "-std=gnu++11"}], "defines": [{"backtrace": 3, "define": "MAIN_APP_WINDOW_TITLE=\"CSPC_UploadPitchDataApp_V0.0.0\""}, {"backtrace": 2, "define": "QT_CORE_LIB"}, {"backtrace": 2, "define": "QT_GUI_LIB"}, {"backtrace": 4, "define": "QT_MESSAGELOGCONTEXT"}, {"backtrace": 2, "define": "QT_NO_DEBUG"}, {"backtrace": 2, "define": "QT_SERIALPORT_LIB"}, {"backtrace": 2, "define": "QT_SQL_LIB"}, {"backtrace": 2, "define": "QT_WIDGETS_LIB"}], "includes": [{"path": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build"}, {"path": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp"}, {"backtrace": 0, "path": "F:/13_Ya<PERSON>-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build/CSPC_UploadPitchDataApp_autogen/include"}, {"backtrace": 2, "isSystem": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include"}, {"backtrace": 2, "isSystem": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtWidgets"}, {"backtrace": 2, "isSystem": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtANGLE"}, {"backtrace": 2, "isSystem": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/./mkspecs/win32-g++"}, {"backtrace": 2, "isSystem": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtSql"}, {"backtrace": 2, "isSystem": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtSerialPort"}], "language": "CXX", "languageStandard": {"backtraces": [2], "standard": "11"}, "sourceIndexes": [0, 1, 2, 6, 8, 10, 12, 14, 16, 18, 20, 23, 30]}, {"defines": [{"backtrace": 3, "define": "MAIN_APP_WINDOW_TITLE=\"CSPC_UploadPitchDataApp_V0.0.0\""}, {"backtrace": 2, "define": "QT_CORE_LIB"}, {"backtrace": 2, "define": "QT_GUI_LIB"}, {"backtrace": 4, "define": "QT_MESSAGELOGCONTEXT"}, {"backtrace": 2, "define": "QT_NO_DEBUG"}, {"backtrace": 2, "define": "QT_SERIALPORT_LIB"}, {"backtrace": 2, "define": "QT_SQL_LIB"}, {"backtrace": 2, "define": "QT_WIDGETS_LIB"}], "includes": [{"path": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build"}, {"path": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp"}, {"backtrace": 0, "path": "F:/13_Ya<PERSON>-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build/CSPC_UploadPitchDataApp_autogen/include"}, {"backtrace": 2, "isSystem": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include"}, {"backtrace": 2, "isSystem": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtWidgets"}, {"backtrace": 2, "isSystem": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtANGLE"}, {"backtrace": 2, "isSystem": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/./mkspecs/win32-g++"}, {"backtrace": 2, "isSystem": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtSql"}, {"backtrace": 2, "isSystem": true, "path": "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/include/QtSerialPort"}], "language": "RC", "sourceIndexes": [25]}], "dependencies": [{"backtrace": 0, "id": "CSPC_UploadPitchDataApp_autogen::@6890427a1f51a3e7e1df"}], "id": "CSPC_UploadPitchDataApp::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-finput-charset=UTF-8 -Os -DNDEBUG", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 2, "fragment": "D:\\Programs\\Qt\\Qt5.14.2\\5.14.2\\mingw73_64\\lib\\libQt5Widgets.a", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Programs\\Qt\\Qt5.14.2\\5.14.2\\mingw73_64\\lib\\libQt5Sql.a", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Programs\\Qt\\Qt5.14.2\\5.14.2\\mingw73_64\\lib\\libQt5SerialPort.a", "role": "libraries"}, {"fragment": "D:\\Programs\\Qt\\Qt5.14.2\\5.14.2\\mingw73_64\\lib\\libQt5Gui.a", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Programs\\Qt\\Qt5.14.2\\5.14.2\\mingw73_64\\lib\\libQt5Core.a", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "CSPC_UploadPitchDataApp", "nameOnDisk": "CSPC_UploadPitchDataApp.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 6, 8, 10, 12, 14, 16, 18, 20, 23, 25, 30]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [3, 5, 7, 9, 11, 13, 15, 17, 19, 22, 27, 28, 29]}, {"name": "", "sourceIndexes": [4, 21, 24, 26]}, {"name": "CMake Rules", "sourceIndexes": [31]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/CSPC_UploadPitchDataApp_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "widget.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "widget.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "widget.ui", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "DataStore/DataStore.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DataStore/DataStore.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Config/ConfigLoader.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Config/ConfigLoader.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Config/XmlConfig.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Config/XmlConfig.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Pojo/PitchData.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Pojo/PitchData.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Pojo/MESData.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Pojo/MESData.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "CSV/CSVReader.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CSV/CSVReader.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "CSV/CSVWriter.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CSV/CSVWriter.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Comm.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Comm.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Comm.ui", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "MainForm.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MainForm.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "MainForm.ui", "sourceGroupIndex": 2}, {"backtrace": 1, "compileGroupIndex": 1, "path": "version.rc", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "res.qrc", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/CSPC_UploadPitchDataApp_autogen/include/ui_widget.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/CSPC_UploadPitchDataApp_autogen/include/ui_Comm.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/CSPC_UploadPitchDataApp_autogen/include/ui_MainForm.h", "sourceGroupIndex": 1}, {"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/CSPC_UploadPitchDataApp_autogen/EWIEGA46WW/qrc_res.cpp.rule", "sourceGroupIndex": 3}], "type": "EXECUTABLE"}