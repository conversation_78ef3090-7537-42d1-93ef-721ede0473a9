{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.5"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0], "name": "UploadPitchDataApp", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "CSPC_UploadPitchDataApp::@6890427a1f51a3e7e1df", "jsonFile": "target-CSPC_UploadPitchDataApp-MinSizeRel-9aeafb7fa5cc7bca20ef.json", "name": "CSPC_UploadPitchDataApp", "projectIndex": 0}, {"directoryIndex": 0, "id": "CSPC_UploadPitchDataApp_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-CSPC_UploadPitchDataApp_autogen-MinSizeRel-c22161caf9331ed3637c.json", "name": "CSPC_UploadPitchDataApp_autogen", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/build", "source": "F:/13_Ya<PERSON>-<PERSON>er-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp"}, "version": {"major": 2, "minor": 3}}