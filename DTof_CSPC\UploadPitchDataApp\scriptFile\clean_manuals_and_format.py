#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
Description: Clean up and reformat all manual files to use a consistent table format
This script ensures all manual files use the same table format and removes legacy changelog sections
'''

import os
import sys
import re
import datetime

# Constants for file paths
MANUAL_DIR = "build/MinSizeRel/output/bin/manual"
TARGET_LISTS = ['lensAdjust', 'lensAdjust_MEMD', 'lensAdjust_rework', 'comCheck', 'motorMonitor', 'novaCalibration']

def extract_entries_from_manual(manual_path):
    """Extract all changelog entries from a manual file"""
    entries = []

    if not os.path.exists(manual_path):
        print(f"Manual file {manual_path} does not exist")
        return entries
    
    with open(manual_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract entries from table format
    table_header_match = re.search(r'\|\s*文档版本\s*\|\s*软件版本号\s*\|\s*变更日期\s*\|\s*变更内容\s*\|\s*变更人\s*\|', content)
    if table_header_match:
        # Find table rows with version info
        table_rows = re.findall(r'\|\s*(\d+\.\d+)\s*\|\s*[Vv]([^\|]+)\|\s*([^\|]+)\|\s*([^\|]+)\|\s*[^\|]+\|', content)
        
        for doc_version, version, date, content_text in table_rows:
            version = version.strip()
            date = date.strip()
            entries.append({
                'doc_version': doc_version,
                'version': version,
                'date': date,
                'content': content_text.strip()
            })
    
    # Also extract entries from standard changelog format
    changelog_section = re.search(r'## Change Log\s*\n', content)
    if changelog_section:
        # Extract all changelog entries
        changelog_entries = re.findall(r'### Version ([^\s]+)\s*-\s*(\d{4}-\d{2}-\d{2})\s*\n((?:.*\n)+?)(?=###|\Z)', content[changelog_section.end():])
        
        for version, date, content_text in changelog_entries:
            # Convert date format from YYYY-MM-DD to YYYY/MM/DD
            formatted_date = date.replace('-', '/')
            
            # Format the content to match table style (numbered list with <br>)
            lines = [line.strip() for line in content_text.split('\n') if line.strip()]
            formatted_lines = []
            
            for i, line in enumerate(lines, 1):
                # Remove bullet points if present
                if line.startswith('-') or line.startswith('*'):
                    line = line[1:].strip()
                
                # Remove type prefixes like feat:, fix:, etc.
                type_match = re.match(r'(\w+)(?:\([\w\-]+\))?: (.*)', line)
                if type_match:
                    # Just use the description part after the colon
                    line = type_match.group(2)
                
                formatted_lines.append(f"{i}. {line}")
            
            formatted_content = '<br>'.join(formatted_lines)
            
            entries.append({
                'doc_version': '0.0',  # Placeholder, will be assigned properly later
                'version': version,
                'date': formatted_date,
                'content': formatted_content
            })
    
    return entries

def rewrite_manual_with_table_format(manual_path, component_name, entries):
    """Rewrite a manual file with a consistent table format"""
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(manual_path), exist_ok=True)
    
    # Sort entries by date
    entries.sort(key=lambda x: (x['date'], x['version']), reverse=False)
    
    # Assign document versions
    doc_version = 1.0
    for entry in entries:
        if entry['doc_version'] == '0.0':
            doc_version += 0.1
            entry['doc_version'] = f"{doc_version:.1f}"
        else:
            # Try to use the existing doc version
            try:
                existing_doc = float(entry['doc_version'])
                if existing_doc > doc_version:
                    doc_version = existing_doc
            except:
                doc_version += 0.1
                entry['doc_version'] = f"{doc_version:.1f}"
    
    # Remove duplicates - keep entries with unique version and date
    unique_entries = []
    seen = set()
    for entry in entries:
        # Create a key based on the content to catch formatted/unformatted duplicates
        # Remove whitespace and formatting to compare content
        content = re.sub(r'\s+', '', entry['content'])
        content = re.sub(r'<br>', '', content)
        content = re.sub(r'\d+\.', '', content)
        key = (content)
        if key not in seen:
            seen.add(key)
            unique_entries.append(entry)
    
    # Start building the new content
    content = f"# {component_name} Manual\n\n"
    content += "| 文档版本 | 软件版本号  | 变更日期      | 变更内容                                    | 变更人 |\n"
    content += "| ---- | ------ | --------- | --------------------------------------- | --- |\n"
    
    # Add all entries to the table
    for entry in unique_entries:
        content += f"| {entry['doc_version']}  | V{entry['version']} | {entry['date']} | {entry['content']} | x  |\n"
    
    # Preserve any additional content from the original file
    if os.path.exists(manual_path):
        with open(manual_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        # Find the actual content (ignoring tables and changelog sections)
        # Try to find user guide content - look for numbered sections
        user_guide_sections = []
        
        # First try to find numbered sections (1. Title)
        numbered_sections = re.findall(r'(?:^|\n)(\d+\.\s+[^\n]+(?:\n(?!\d+\.\s+|#)[^\n]+)*)', original_content)
        if numbered_sections:
            for section in numbered_sections:
                if "Change Log" not in section and "# " not in section:
                    user_guide_sections.append(section)
        
        # If no numbered sections, try to find content between the table and changelog
        if not user_guide_sections:
            # Find the last table row
            last_table_row = list(re.finditer(r'\|\s*\d+\.\d+\s*\|\s*[Vv][^\|]+\|[^\|]+\|[^\|]+\|[^\|]+\|\s*\n', original_content))
            changelog_section = re.search(r'(?:^|\n)#+\s+Change Log', original_content)
            
            if last_table_row and changelog_section:
                # Get content between last table row and changelog
                between_content = original_content[last_table_row[-1].end():changelog_section.start()]
                if between_content.strip():
                    user_guide_sections.append(between_content.strip())
        
        # Add all user guide sections
        if user_guide_sections:
            content += "\n" + "\n".join(user_guide_sections)
    
    # Write the new content
    with open(manual_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return True

def main():
    """Main function to clean up and reformat all manual files"""
    processed_files = 0
    
    for component in TARGET_LISTS:
        # Manual file path - handle special case for lensAdjust
        if component == "lensAdjust":
            manual_file = "lensAdjust_manual.md"
        else:
            manual_file = f"{component}_manual.md"
        
        manual_path = os.path.join(MANUAL_DIR, component, manual_file)
        
        print(f"Processing {manual_path}...")
        
        # Extract all entries
        entries = extract_entries_from_manual(manual_path)
        
        if entries:
            print(f"Found {len(entries)} entries in {manual_path}")
            # Rewrite the manual with a consistent table format
            if rewrite_manual_with_table_format(manual_path, component, entries):
                processed_files += 1
                print(f"Successfully reformatted {manual_path}")
        else:
            print(f"No entries found in {manual_path}")
    
    print(f"Processed {processed_files} manual files")

if __name__ == "__main__":
    main() 