# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.21

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\Programs\CMake\bin\cmake.exe

# The command to remove a file.
RM = D:\Programs\CMake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/CSPC_UploadPitchDataApp.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/CSPC_UploadPitchDataApp.dir/clean
clean: CMakeFiles/CSPC_UploadPitchDataApp_autogen.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/CSPC_UploadPitchDataApp.dir

# All Build rule for target.
CMakeFiles/CSPC_UploadPitchDataApp.dir/all: CMakeFiles/CSPC_UploadPitchDataApp_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16 "Built target CSPC_UploadPitchDataApp"
.PHONY : CMakeFiles/CSPC_UploadPitchDataApp.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/CSPC_UploadPitchDataApp.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles 17
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/CSPC_UploadPitchDataApp.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles 0
.PHONY : CMakeFiles/CSPC_UploadPitchDataApp.dir/rule

# Convenience name for target.
CSPC_UploadPitchDataApp: CMakeFiles/CSPC_UploadPitchDataApp.dir/rule
.PHONY : CSPC_UploadPitchDataApp

# clean rule for target.
CMakeFiles/CSPC_UploadPitchDataApp.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp.dir/clean
.PHONY : CMakeFiles/CSPC_UploadPitchDataApp.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/CSPC_UploadPitchDataApp_autogen.dir

# All Build rule for target.
CMakeFiles/CSPC_UploadPitchDataApp_autogen.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp_autogen.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp_autogen.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles --progress-num=17 "Built target CSPC_UploadPitchDataApp_autogen"
.PHONY : CMakeFiles/CSPC_UploadPitchDataApp_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/CSPC_UploadPitchDataApp_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/CSPC_UploadPitchDataApp_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start F:\13_Yapha-Laser-DTof2dMS\development\tool\yapha-proj-mes-02\DTof_CSPC\UploadPitchDataApp\build\CMakeFiles 0
.PHONY : CMakeFiles/CSPC_UploadPitchDataApp_autogen.dir/rule

# Convenience name for target.
CSPC_UploadPitchDataApp_autogen: CMakeFiles/CSPC_UploadPitchDataApp_autogen.dir/rule
.PHONY : CSPC_UploadPitchDataApp_autogen

# clean rule for target.
CMakeFiles/CSPC_UploadPitchDataApp_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CSPC_UploadPitchDataApp_autogen.dir\build.make CMakeFiles/CSPC_UploadPitchDataApp_autogen.dir/clean
.PHONY : CMakeFiles/CSPC_UploadPitchDataApp_autogen.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

