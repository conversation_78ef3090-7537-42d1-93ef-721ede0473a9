/**
 * MES Upload Logic Test (English Version)
 * Test the core logic of MES data upload functionality
 */

#include <iostream>
#include <string>
#include <vector>

enum class UploadStatus {
    NotUploaded = 0,
    Uploaded = 1,
    Failed = -1
};

class TestPitchData {
public:
    std::string nbr;
    std::string mcuID;
    UploadStatus uploadStatus;
    
    TestPitchData(const std::string& n, const std::string& m, UploadStatus s = UploadStatus::NotUploaded)
        : nbr(n), mcuID(m), uploadStatus(s) {}
    
    bool needsProcessing() const {
        return uploadStatus == UploadStatus::NotUploaded || uploadStatus == UploadStatus::Failed;
    }
    
    bool isUploaded() const {
        return uploadStatus == UploadStatus::Uploaded;
    }
};

class TestCSVReader {
public:
    std::vector<TestPitchData> removeDuplicatesKeepLatest(const std::vector<TestPitchData>& data) {
        std::vector<TestPitchData> result;
        std::vector<std::string> processedNbrs;
        
        // Process from back to front, keep latest data
        for (int i = data.size() - 1; i >= 0; i--) {
            const TestPitchData& item = data[i];
            
            bool found = false;
            for (const std::string& nbr : processedNbrs) {
                if (nbr == item.nbr) {
                    found = true;
                    break;
                }
            }
            
            if (!found) {
                result.insert(result.begin(), item);
                processedNbrs.push_back(item.nbr);
            }
        }
        
        std::cout << "Deduplication - Original:" << data.size() << " After:" << result.size() << std::endl;
        return result;
    }
    
    std::vector<TestPitchData> filterNeedsProcessing(const std::vector<TestPitchData>& data) {
        std::vector<TestPitchData> result;
        
        for (const TestPitchData& item : data) {
            if (item.needsProcessing()) {
                result.push_back(item);
            }
        }
        
        std::cout << "Filter processing - Total:" << data.size() << " Needs:" << result.size() << std::endl;
        return result;
    }
    
    std::vector<TestPitchData> getActualUploadData(const std::vector<TestPitchData>& data) {
        std::vector<TestPitchData> uniqueData = removeDuplicatesKeepLatest(data);
        std::vector<TestPitchData> needsProcessing = filterNeedsProcessing(uniqueData);
        
        std::cout << "Actual upload data - Original:" << data.size() 
                  << " Unique:" << uniqueData.size() 
                  << " ToUpload:" << needsProcessing.size() << std::endl;
        
        return needsProcessing;
    }
};

void runTest() {
    std::cout << "=== MES Upload Logic Test Started ===" << std::endl;
    
    // Create test data with duplicates and different statuses
    std::vector<TestPitchData> testData = {
        TestPitchData("00000001", "MCU001", UploadStatus::NotUploaded),
        TestPitchData("00000002", "MCU002", UploadStatus::Uploaded),
        TestPitchData("00000001", "MCU001", UploadStatus::NotUploaded),  // Duplicate, latest
        TestPitchData("00000003", "MCU003", UploadStatus::Failed),
        TestPitchData("00000004", "MCU004", UploadStatus::NotUploaded),
        TestPitchData("00000002", "MCU002", UploadStatus::Failed),       // Duplicate, latest
        TestPitchData("00000005", "MCU005", UploadStatus::Uploaded),
    };
    
    std::cout << "\n--- Original Test Data ---" << std::endl;
    for (size_t i = 0; i < testData.size(); i++) {
        std::cout << i+1 << ". NBR:" << testData[i].nbr 
                  << " Status:" << static_cast<int>(testData[i].uploadStatus) << std::endl;
    }
    
    TestCSVReader reader;
    
    // Test 1: Deduplication
    std::cout << "\n--- Test 1: Deduplication ---" << std::endl;
    std::vector<TestPitchData> uniqueData = reader.removeDuplicatesKeepLatest(testData);
    
    std::cout << "After deduplication:" << std::endl;
    for (size_t i = 0; i < uniqueData.size(); i++) {
        std::cout << i+1 << ". NBR:" << uniqueData[i].nbr 
                  << " Status:" << static_cast<int>(uniqueData[i].uploadStatus) << std::endl;
    }
    
    // Test 2: Filter needs processing
    std::cout << "\n--- Test 2: Filter Needs Processing ---" << std::endl;
    std::vector<TestPitchData> needsProcessing = reader.filterNeedsProcessing(uniqueData);
    
    std::cout << "Needs processing:" << std::endl;
    for (size_t i = 0; i < needsProcessing.size(); i++) {
        std::cout << i+1 << ". NBR:" << needsProcessing[i].nbr 
                  << " Status:" << static_cast<int>(needsProcessing[i].uploadStatus) << std::endl;
    }
    
    // Test 3: Get actual upload data
    std::cout << "\n--- Test 3: Get Actual Upload Data ---" << std::endl;
    std::vector<TestPitchData> actualUploadData = reader.getActualUploadData(testData);
    
    std::cout << "Actual upload data:" << std::endl;
    for (size_t i = 0; i < actualUploadData.size(); i++) {
        std::cout << i+1 << ". NBR:" << actualUploadData[i].nbr 
                  << " Status:" << static_cast<int>(actualUploadData[i].uploadStatus) << std::endl;
    }
    
    // Verify results
    std::cout << "\n--- Verification ---" << std::endl;
    bool testPassed = true;
    
    // Check deduplication (should have 5 unique items)
    if (uniqueData.size() != 5) {
        std::cout << "FAIL: Deduplication test - Expected 5, got " << uniqueData.size() << std::endl;
        testPassed = false;
    } else {
        std::cout << "PASS: Deduplication test" << std::endl;
    }
    
    // Check filtering (should exclude uploaded data)
    bool hasUploadedData = false;
    for (const TestPitchData& item : actualUploadData) {
        if (item.isUploaded()) {
            hasUploadedData = true;
            break;
        }
    }
    
    if (hasUploadedData) {
        std::cout << "FAIL: Filter test - Contains uploaded data" << std::endl;
        testPassed = false;
    } else {
        std::cout << "PASS: Filter test" << std::endl;
    }
    
    // Check specific logic: NBR 00000001 should be NotUploaded (latest)
    bool nbr001Found = false;
    for (const TestPitchData& item : uniqueData) {
        if (item.nbr == "00000001") {
            if (item.uploadStatus == UploadStatus::NotUploaded) {
                std::cout << "PASS: Latest data kept for NBR 00000001" << std::endl;
            } else {
                std::cout << "FAIL: Wrong status for NBR 00000001" << std::endl;
                testPassed = false;
            }
            nbr001Found = true;
            break;
        }
    }
    
    if (!nbr001Found) {
        std::cout << "FAIL: NBR 00000001 not found in unique data" << std::endl;
        testPassed = false;
    }
    
    std::cout << "\n=== Test Result: " << (testPassed ? "ALL PASSED" : "SOME FAILED") << " ===" << std::endl;
    
    // Summary statistics
    std::cout << "\n--- Summary Statistics ---" << std::endl;
    std::cout << "Original data count: " << testData.size() << std::endl;
    std::cout << "After deduplication: " << uniqueData.size() << std::endl;
    std::cout << "Needs processing: " << needsProcessing.size() << std::endl;
    std::cout << "Actual upload needed: " << actualUploadData.size() << std::endl;
    
    double reductionRate = (1.0 - (double)actualUploadData.size() / testData.size()) * 100;
    std::cout << "Data reduction rate: " << reductionRate << "%" << std::endl;
}

int main() {
    runTest();
    return 0;
}
