#include "Comm.h"
#include "ui_Comm.h"

#include <QMessageBox>
#include <QSerialPortInfo>

#include <Windows.h>
#include <dbt.h>

#include <QDebug>

Comm::Comm(QWidget *parent) : QWidget(parent), ui(new Ui::Comm) {
    ui->setupUi(this);
    setWindowTitle("串口配置V1.0");
    qApp->installNativeEventFilter(this);

    updatePort();

    connect(&virtualSerial, &QSerialPort::readyRead, [&]() -> void {
        QString cmd = QString::fromUtf8(virtualSerial.readAll());
        emit    parseVirtualData(cmd);
    });

    connect(&lidarSerial, &QSerialPort::readyRead, [&]() -> void {
        auto data = lidarSerial.readAll();
        if (type > CmdType::NoCmd)
            emit parseLidarData(data);
    });

    connect(this, &Comm::parsed, [&](const QString &info) -> void {
        timer->stop();
        if (info != "") {
            virtualSerial.write(info.toUtf8());
        }
    });

    connect(this, &Comm::parseVirtualData, this, &Comm::parseVirtualDataSlot);
    connect(this, &Comm::parseLidarData, this, &Comm::parseLidarDataSlot);

    timer = new QTimer(this);
    timer->setInterval(500);
    timer->setSingleShot(true);
    connect(timer, &QTimer::timeout, [&]() -> void {
        type = CmdType::NoCmd;
        virtualSerial.write("<0>");
    });
}

Comm::~Comm() {
    delete ui;
}

bool Comm::nativeEventFilter(const QByteArray &eventType, void *message, long *result) {
#if WIN32
    MSG *msg     = reinterpret_cast<MSG *>(message);
    int  msgType = msg->message;
    if (msgType == WM_DEVICECHANGE) {
        PDEV_BROADCAST_HDR lpdb = (PDEV_BROADCAST_HDR)msg->lParam;
        switch (msg->wParam) {
        case DBT_DEVICEARRIVAL:
            if (lpdb->dbch_devicetype == DBT_DEVTYP_PORT) {
                updatePort();
            }
            break;
        case DBT_DEVICEREMOVECOMPLETE:
            if (lpdb->dbch_devicetype == DBT_DEVTYP_PORT) {
                updatePort();
            }
            break;
        case DBT_DEVNODES_CHANGED:
            break;
        default:
            break;
        }
    }
#elif

#endif

    return QWidget::nativeEvent(eventType, message, result);
}

void Comm::on_virtualUartBtn_clicked() {
    if ("打开" == ui->virtualUartBtn->text()) {
        QString portName = ui->virtualUartCombox->currentText();
        virtualSerial.setPortName(portName);
        virtualSerial.setBaudRate(115200);
        virtualSerial.setDataBits(QSerialPort::Data8);
        virtualSerial.setStopBits(QSerialPort::OneStop);
        virtualSerial.setParity(QSerialPort::NoParity);
        virtualSerial.setFlowControl(QSerialPort::NoFlowControl);

        if (virtualSerial.open(QIODevice::ReadWrite)) {
            ui->virtualUartBtn->setText("关闭");
        } else {
            QMessageBox::information(this, "提示", "无法打开此串口,请检查");
        }

    } else {
        if (virtualSerial.isOpen()) {
            virtualSerial.close();
            ui->virtualUartBtn->setText("打开");
        } else {
            QMessageBox::information(this, "提示", "串口没有打开,关闭串口失败");
        }
    }
}

void Comm::on_lidarUartBtn_clicked() {
    if ("打开" == ui->lidarUartBtn->text()) {
        QString portName = ui->lidarUartCombox->currentText();
        lidarSerial.setPortName(portName);
        lidarSerial.setBaudRate(230400);
        lidarSerial.setDataBits(QSerialPort::Data8);
        lidarSerial.setStopBits(QSerialPort::OneStop);
        lidarSerial.setParity(QSerialPort::NoParity);
        lidarSerial.setFlowControl(QSerialPort::NoFlowControl);

        if (lidarSerial.open(QIODevice::ReadWrite)) {
            ui->lidarUartBtn->setText("关闭");
        } else {
            QMessageBox::information(this, "提示", "无法打开此串口,请检查");
        }

    } else {
        if (lidarSerial.isOpen()) {
            lidarSerial.close();
            ui->lidarUartBtn->setText("打开");
        } else {
            QMessageBox::information(this, "提示", "串口没有打开,关闭串口失败");
        }
    }
}

/**
 * @brief T5 默认上电转（running mode) A5 05 F0 AD 02 00 00 00 FE 01
 * T5默认上电不转（standby mode) A5 05 F0 AD 02 00 00 00 FD 02
 *
 * @param data
 */
void Comm::parseVirtualDataSlot(const QString &data) {
    QByteArray cmd;
    if ("<MCUID>" == data) {
        type = CmdType::MCUID;
        cmd.push_back((char)0xA5);
        cmd.push_back((char)0x09);
        cmd.push_back((char)0xA3);
        cmd.push_back((char)0x0F);
        cmd.push_back((char)0x00);
        cmd.push_back((char)0x00);
        lidarSerial.write(cmd);
        timer->start();
    } else if ("<SoftwareVersion>" == data) {
        type = CmdType::SoftwareVersion;
        cmd.push_back((char)0xA5);
        cmd.push_back((char)0x09);
        cmd.push_back((char)0xA4);
        cmd.push_back((char)0x08);
        cmd.push_back((char)0x00);
        cmd.push_back((char)0x00);
        lidarSerial.write(cmd);
        timer->start();

        // running
        cmd.clear();
        cmd.push_back((char)0xA5);
        cmd.push_back((char)0x05);
        cmd.push_back((char)0xF0);
        cmd.push_back((char)0xAD);
        cmd.push_back((char)0x02);
        cmd.push_back((char)0x00);
        cmd.push_back((char)0x00);
        cmd.push_back((char)0x00);
        cmd.push_back((char)0xFE);
        cmd.push_back((char)0x01);

        lidarSerial.write(cmd);
        timer->start();
    } else {
        type = CmdType::NoCmd;
        virtualSerial.write("<0>");
    }
}

void Comm::parseLidarDataSlot(QByteArray data) {
    static QByteArray dataLocal;
    dataLocal += data;

    if (CmdType::MCUID == type) {
        parseDataMcuId(dataLocal);
    } else if (CmdType::SoftwareVersion == type) {
        parseDataSoftwareVersion(dataLocal);
    } else if (CmdType::eLIDAR_RUNNING == type) {
        parseDataCommonAck(dataLocal);
    }
}

void Comm::updatePort() {
    ui->virtualUartCombox->clear();
    ui->lidarUartCombox->clear();
    for (const auto &item : QSerialPortInfo::availablePorts()) {
        ui->virtualUartCombox->addItem(item.portName());
        ui->lidarUartCombox->addItem(item.portName());
    }
}

void Comm::parseDataMcuId(QByteArray &data) {
#define CMD_ACK_LENGTH 18
    qDebug() << QString::fromUtf8("Start parsing MCUID.");
    for (int i = 0; i < data.length(); ++i) {
        if (!isValidForIndex(i + CMD_ACK_LENGTH - 1, data.length() - 1))
            break;

        if (data.at(i) == (char)0xA5 && data.at(i + 1) == (char)0x1A && data.at(i + 2) == (char)0xA3) {
            uint8_t checkXor = 0;
            for (int j = i; j < i + CMD_ACK_LENGTH; ++j)
                if (j != i + 3)
                    checkXor ^= data.at(j);

            if ((char)checkXor != data.at(i + 3))
                continue;
            QByteArray dataArray{data.mid(i + 6, 6 * 2)};

            auto mcuid = toHexString(dataArray);
            qDebug() << QString::fromUtf8("Parsing completed.:") << mcuid;
            type = CmdType::NoCmd;
            data.clear();

            emit parsed(mcuid);
            return;
        }
    }
}

void Comm::parseDataSoftwareVersion(QByteArray &data) {
#define CMD_ACK_LENGTH_V0 14
#define CMD_ACK_LENGTH_V1 18
    qDebug() << QString::fromUtf8("Start parsing the firmware version.");
    for (int i = 0; i < data.length(); ++i) {
        if ((i + 2) > data.length() || data.at(i) != (char)0xA5 || data.at(i + 1) != (char)0x1A || data.at(i + 2) != (char)0xA4)
            continue;

        int ackLength = INT_MAX;
        if (i + 4 <= data.length()) {

            auto dataLength = data.at(i + 4);
            if (dataLength == (char)0x04)
                ackLength = CMD_ACK_LENGTH_V0;
            else if ((char)0x06 == dataLength)
                ackLength = CMD_ACK_LENGTH_V1;

            if ((i + ackLength) > data.length())
                continue;
        }

        uint8_t checkXor = 0;
        for (int j = i; j < i + ackLength; ++j) {
            if (j != i + 3)
                checkXor ^= data.at(j);
        }

        if ((char)checkXor != data.at(i + 3))
            continue;

        QVector<int> stringList;
        for (int j = 6; j <= 12; ++j)
            stringList.append(QString("%1").arg(static_cast<quint8>(data.at(i + j)), 2, 16, QLatin1Char('0')).toUpper().toInt(nullptr, 16));

        QString version = QString("V%1.%2.%3.%4.%5.%6.%7")
                              .arg(stringList[0])
                              .arg(stringList[1])
                              .arg(stringList[2])
                              .arg(stringList[3])
                              .arg(stringList[4])
                              .arg(stringList[5])
                              .arg(stringList[6]);

        qDebug() << QString::fromUtf8("Parsing completed:") << version;
        type = CmdType::NoCmd;
        data.clear();
        emit parsed(version);
        return;
    }
}

void Comm::parseDataCommonAck(QByteArray &data) {
    const static int CMD_ACK_LENGTH_COMMON = 4;

    qDebug() << QString::fromUtf8("sensor normal ack.");

    for (int i = 0; i < data.length(); ++i) {
        if ((i + 2) > data.length() || data.at(i) != (char)0xA5 || data.at(i + 1) != (char)0x16 || data.at(i + 2) != (char)0xF0)
            continue;
    }
    for (int i = 0; i < data.length(); ++i) {
        if (!isValidForIndex(i + CMD_ACK_LENGTH_COMMON - 1, data.length() - 1))
            break;

        if (data.at(i) == (char)0xA5 && data.at(i + 1) == (char)0x16 && data.at(i + 2) == (char)0xF0) {
            uint8_t checkXor = 0;

            // CHECK XOR
            // if (0) {
            //     continue;
            // }

            type = CmdType::NoCmd;
            data.clear();
            emit parsed("");

            return;
        }
    }
}
