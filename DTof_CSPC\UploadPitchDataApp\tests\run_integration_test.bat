@echo off
echo ========================================
echo MES功能集成测试脚本
echo ========================================
echo.

set APP_DIR=..\build\bin
set TEST_DIR=%~dp0
set LOG_DIR=%APP_DIR%\Log
set BACKUP_DIR=%APP_DIR%\backup

echo 测试环境信息:
echo - 应用程序目录: %APP_DIR%
echo - 测试目录: %TEST_DIR%
echo - 日志目录: %LOG_DIR%
echo - 备份目录: %BACKUP_DIR%
echo.

echo ========================================
echo 1. 检查测试环境
echo ========================================

:: 检查主程序是否存在
if not exist "%APP_DIR%\CSPC_UploadPitchDataApp.exe" (
    echo ❌ 主程序不存在: %APP_DIR%\CSPC_UploadPitchDataApp.exe
    echo 请先编译主项目
    pause
    exit /b 1
)
echo ✅ 主程序存在

:: 检查测试数据文件
if not exist "%APP_DIR%\俯仰角视觉检测_195_20250623143833252.csv" (
    echo ❌ 测试数据文件不存在
    echo 请确保测试数据文件在bin目录下
    pause
    exit /b 1
)
echo ✅ 测试数据文件存在

:: 创建必要的目录
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"
if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"
echo ✅ 目录结构检查完成

echo.
echo ========================================
echo 2. 备份现有文件
echo ========================================

:: 备份现有的结果文件
set TIMESTAMP=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%

if exist "%APP_DIR%\*上传*.csv" (
    echo 备份现有结果文件...
    copy "%APP_DIR%\*上传*.csv" "%BACKUP_DIR%\" >nul 2>&1
    del "%APP_DIR%\*上传*.csv" >nul 2>&1
)

if exist "%APP_DIR%\*异常*.csv" (
    echo 备份现有异常文件...
    copy "%APP_DIR%\*异常*.csv" "%BACKUP_DIR%\" >nul 2>&1
    del "%APP_DIR%\*异常*.csv" >nul 2>&1
)

if exist "%APP_DIR%\*工作副本*.csv" (
    echo 备份现有工作拷贝...
    copy "%APP_DIR%\*工作副本*.csv" "%BACKUP_DIR%\" >nul 2>&1
    del "%APP_DIR%\*工作副本*.csv" >nul 2>&1
)

echo ✅ 文件备份完成

echo.
echo ========================================
echo 3. 运行逻辑测试
echo ========================================

if exist "%TEST_DIR%\test_logic.exe" (
    echo 运行逻辑测试...
    cd /d "%TEST_DIR%"
    test_logic.exe
    echo ✅ 逻辑测试完成
) else (
    echo ⚠️  逻辑测试程序不存在，跳过
)

echo.
echo ========================================
echo 4. 检查CSV处理功能
echo ========================================

cd /d "%APP_DIR%"

:: 检查原始文件
echo 检查原始测试数据文件...
for %%f in (俯仰角视觉检测_195_20250623143833252.csv) do (
    echo - 文件名: %%f
    echo - 文件大小: %%~zf 字节
    echo - 修改时间: %%~tf
)

:: 模拟创建工作拷贝（通过复制文件并添加状态列）
echo.
echo 模拟创建工作拷贝...
set WORKING_COPY=俯仰角视觉检测_195_20250623143833252_工作副本_%TIMESTAMP%.csv

:: 复制原文件并在每行末尾添加逗号（模拟状态列）
powershell -Command "(Get-Content '俯仰角视觉检测_195_20250623143833252.csv') | ForEach-Object { $_ + ',' } | Set-Content '%WORKING_COPY%'"

if exist "%WORKING_COPY%" (
    echo ✅ 工作拷贝创建成功: %WORKING_COPY%
    for %%f in ("%WORKING_COPY%") do (
        echo - 工作拷贝大小: %%~zf 字节
    )
) else (
    echo ❌ 工作拷贝创建失败
)

echo.
echo ========================================
echo 5. 检查配置文件
echo ========================================

if exist "MesInfo.xml" (
    echo ✅ 配置文件存在: MesInfo.xml
    for %%f in (MesInfo.xml) do (
        echo - 配置文件大小: %%~zf 字节
        echo - 修改时间: %%~tf
    )
    
    :: 显示配置文件内容的前几行
    echo.
    echo 配置文件内容预览:
    powershell -Command "Get-Content 'MesInfo.xml' | Select-Object -First 10"
) else (
    echo ⚠️  配置文件不存在: MesInfo.xml
    echo 程序将使用默认配置
)

echo.
echo ========================================
echo 6. 检查日志功能
echo ========================================

:: 检查日志目录
if exist "%LOG_DIR%" (
    echo ✅ 日志目录存在: %LOG_DIR%
    
    :: 列出现有日志文件
    echo.
    echo 现有日志文件:
    dir "%LOG_DIR%\*.log" /b 2>nul
    if errorlevel 1 (
        echo - 暂无日志文件
    )
) else (
    echo ⚠️  日志目录不存在，已创建: %LOG_DIR%
)

echo.
echo ========================================
echo 7. 数据统计分析
echo ========================================

:: 使用PowerShell分析CSV数据
echo 分析测试数据...
powershell -Command "& {
    $csvFile = '俯仰角视觉检测_195_20250623143833252.csv'
    if (Test-Path $csvFile) {
        $content = Get-Content $csvFile
        $lineCount = $content.Count
        Write-Host \"- 总行数: $lineCount\"
        
        if ($lineCount -gt 0) {
            $firstLine = $content[0]
            $fields = $firstLine.Split(',')
            Write-Host \"- 列数: $($fields.Count)\"
            Write-Host \"- 第一行: $firstLine\"
            
            if ($lineCount -gt 1) {
                $lastLine = $content[-1]
                Write-Host \"- 最后一行: $lastLine\"
            }
        }
        
        # 检查是否有重复的电机标签
        $nbrColumn = @()
        for ($i = 0; $i -lt $content.Count; $i++) {
            $fields = $content[$i].Split(',')
            if ($fields.Count -gt 0) {
                $nbrColumn += $fields[0]
            }
        }
        
        $uniqueNbrs = $nbrColumn | Sort-Object | Get-Unique
        Write-Host \"- 唯一电机标签数: $($uniqueNbrs.Count)\"
        Write-Host \"- 重复数据: $(($nbrColumn.Count - $uniqueNbrs.Count))\"
    }
}"

echo.
echo ========================================
echo 8. 测试总结
echo ========================================

echo 测试完成时间: %date% %time%
echo.
echo 测试项目检查结果:
echo ✅ 主程序编译状态: 正常
echo ✅ 测试数据文件: 存在
echo ✅ 工作拷贝功能: 模拟成功
echo ✅ 目录结构: 正常
echo ✅ 数据分析: 完成

if exist "%WORKING_COPY%" (
    echo ✅ 生成的工作拷贝: %WORKING_COPY%
)

echo.
echo 注意事项:
echo 1. 此测试未连接真实MES数据库
echo 2. 工作拷贝文件已生成，可用于进一步测试
echo 3. 如需测试真实上传功能，请配置MES数据库连接
echo 4. 建议在测试环境中进行完整的集成测试

echo.
echo ========================================
echo 测试完成
echo ========================================

pause
