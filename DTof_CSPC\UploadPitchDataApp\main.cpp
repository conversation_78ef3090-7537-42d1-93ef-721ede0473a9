#include <QApplication>
#include <QDateTime>
#include <QDir>
#include <QFile>
#include <QString>
#include <QTextCodec>
#include <QTextStream>
#include "MainForm.h"


// 自定义的消息处理函数
static void messageHandler(QtMsgType type, const QMessageLogContext &context, const QString &msg)
{
    // 获取当前时间和日期
    QDateTime currentDateTime = QDateTime::currentDateTime();
    QString filePath = "./Log/log_" + currentDateTime.toString("yyyy-MM-dd") + ".log";

    QDir dir = QFileInfo(filePath).dir();
    if (!dir.exists() && !dir.mkpath(dir.absolutePath())) {
        return;
    }

    // 打开日志文件
    QFile logFile(filePath);
    logFile.open(QIODevice::WriteOnly | QIODevice::Append);

    // 写入错误信息到日志文件中
    QTextStream out(&logFile);
    out << currentDateTime.toString("yyyy-MM-dd hh:mm:ss") << " ";

    switch (type) {
        case QtDebugMsg:
            out << "[DEBUG] ";
            break;
        case QtInfoMsg:
            out << "[INFO] ";
            break;
        case QtWarningMsg:
            out << "[WARNING] ";
            break;
        case QtCriticalMsg:
            out << "[CRITICAL] ";
            break;
        case QtFatalMsg:
            out << "[FATAL] ";
            break;
    }

    out << msg.toLocal8Bit() << " (" << context.file << ":" << context.line << ", " << context.function << ")" << endl;

    // 关闭日志文件
    logFile.close();
}


int main(int argc, char *argv[])
{
    QApplication a(argc, argv);
    //初始化qdebug的输出重定向到文件
    qInstallMessageHandler(messageHandler);


    //设置中文编码
    a.setFont(QFont("Microsoft Yahei", 9));
#if (QT_VERSION <= QT_VERSION_CHECK(5,0,0))
#if _MSC_VER
    QTextCodec *codec = QTextCodec::codecForName("GBK");
#else
    QTextCodec *codec = QTextCodec::codecForName("UTF-8");
#endif
    QTextCodec::setCodecForLocale(codec);
    QTextCodec::setCodecForCStrings(codec);
    QTextCodec::setCodecForTr(codec);
#else
    QTextCodec *codec = QTextCodec::codecForName("UTF-8");
    QTextCodec::setCodecForLocale(codec);
#endif

    Main w;
    w.show();
    return a.exec();
}
